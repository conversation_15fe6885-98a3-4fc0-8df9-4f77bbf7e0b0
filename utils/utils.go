package utils

import (
	"crypto/rand"
	"math/big"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

// GenerateRandomString 生成指定长度的随机字符串，使用加密随机数生成器
func GenerateRandomString(n int) (string, error) {
	var sb strings.Builder
	charSetLength := big.NewInt(int64(len(letterBytes)))

	for i := 0; i < n; i++ {
		// 生成一个随机数并选择对应的字符
		idx, err := rand.Int(rand.Reader, charSetLength)
		if err != nil {
			return "", err // 如果发生错误，返回
		}
		sb.WriteByte(letterBytes[idx.Int64()])
	}

	return sb.String(), nil
}

// Contains 检查字符串是否在切片中
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// SetSysctl 设置sysctl参数
func SetSysctl(key, value string) error {
	path := filepath.Join("/proc/sys", key)
	file, err := os.OpenFile(path, os.O_WRONLY, 0)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.WriteString(value)
	return err
}

// GetObjectIDFromParam 从请求参数中获取 ObjectID
func GetObjectIDFromParam(c *gin.Context, param string) (primitive.ObjectID, error) {
	idStr := c.Param(param)
	return primitive.ObjectIDFromHex(idStr)
}

// GetPaginationParams extracts and validates pagination parameters from the request
func GetPaginationParams(c *gin.Context) (pageNum, pageSizeNum int) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")

	pageNum, err := strconv.Atoi(pageStr)
	if err != nil || pageNum < 1 {
		pageNum = 1
	}

	pageSizeNum, err = strconv.Atoi(pageSizeStr)
	if err != nil || pageSizeNum < 1 {
		pageSizeNum = 10
	}

	return pageNum, pageSizeNum
}
