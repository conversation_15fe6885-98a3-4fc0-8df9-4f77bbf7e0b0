package utils

import (
	"beacon/cloud/config"
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Claims 结构体定义 JWT 的负载
type Claims struct {
	Username     string `json:"username"`      // 用户名
	EnterpriseID string `json:"enterprise_id"` // 企业ID
	jwt.RegisteredClaims
}

// GenerateToken 生成 JWT
func GenerateToken(username, enterpriseID string) (string, error) {
	// 设置 JWT 的过期时间
	expirationTime := time.Now().Add(24 * time.Hour)

	// 创建 JWT 的负载（Claims）
	claims := &Claims{
		Username:     username,
		EnterpriseID: enterpriseID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime), // 过期时间
			IssuedAt:  jwt.NewNumericDate(time.Now()),     // 签发时间
			NotBefore: jwt.NewNumericDate(time.Now()),     // 生效时间
			Issuer:    "BeaconGlobalTechnology",           // 签发者
		},
	}

	// 使用 HS256 签名方法创建 JWT
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 使用 JWT Secret 签名并返回 Token
	return token.SignedString([]byte(config.AppConfig.JwtSecret))
}

// ParseToken 解析并验证 JWT
func ParseToken(tokenString string) (*Claims, error) {
	// 解析 Token
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法是否为 HS256
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		// 返回 JWT Secret
		return []byte(config.AppConfig.JwtSecret), nil
	})

	// 处理解析错误
	if err != nil {
		return nil, err
	}

	// 验证 Token 是否有效
	if !token.Valid {
		return nil, jwt.ErrSignatureInvalid
	}

	// 返回解析后的 Claims
	return claims, nil
}

// GenerateRandomSecret 生成随机的 JWT Secret
func GenerateRandomSecret(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+"
	secret := make([]byte, length)
	for i := range secret {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		secret[i] = charset[num.Int64()]
	}
	return string(secret), nil
}

// randInt 使用 crypto/rand 生成一个指定范围内的随机整数
func randInt(max int) (int, error) {
	// 使用 crypto/rand 生成一个随机数
	n, err := rand.Int(rand.Reader, big.NewInt(int64(max)))
	if err != nil {
		return 0, err
	}
	return int(n.Int64()), nil
}

// generateRandomPassword 生成一个包含大写字母、小写字母、数字和特殊字符的随机密码
func GenerateRandomPassword() (string, error) {
	// 定义密码中允许的字符集
	lowercase := "abcdefghijklmnopqrstuvwxyz"
	uppercase := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	digits := "0123456789"
	specialChars := "@$!%*?&"

	// 合并所有字符集
	allChars := lowercase + uppercase + digits + specialChars

	// 使用 crypto/rand 来生成密码，确保密码的随机性
	var password strings.Builder
	for i := 0; i < 16; i++ {
		// 从 allChars 中随机选择一个字符
		index, err := randInt(len(allChars))
		if err != nil {
			return "", err
		}
		password.WriteByte(allChars[index])
	}

	return password.String(), nil
}

// 非空检查
// CheckRequiredFields 检查给定的字段是否为空
func CheckRequiredFields(fields ...interface{}) error {
	if len(fields)%2 != 0 {
		return fmt.Errorf("invalid number of arguments: fields and values must be provided in pairs")
	}

	for i := 0; i < len(fields); i += 2 {
		fieldName, ok := fields[i].(string)
		if !ok {
			return fmt.Errorf("field name must be a string")
		}

		fieldValue := fields[i+1]
		switch v := fieldValue.(type) {
		case string:
			if v == "" {
				return fmt.Errorf("%s is required", fieldName)
			}
		default:
			if v == nil {
				return fmt.Errorf("%s is required", fieldName)
			}
		}
	}
	return nil
}
