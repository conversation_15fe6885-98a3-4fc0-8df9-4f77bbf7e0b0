MongoDB:
  username: beacon
  password: beacON028
  host: mongodb
  port: "27017"
  dbname: "beacon"

TaosDB:
  username: root
  password: taosdata
  host: tdengine
  port: "6041"
  dbname: "beacon"

Redis:
  host: "redis"
  port: "6379"
  password: "beacON028"
  db: 0

mail:
  host: smtp.exmail.qq.com
  port: "465"
  username: <EMAIL>
  password: 147258aA@

jwt_secret: www.beaconglobaltech.com

host: *************** # 服务器ip或域名，用于指定wireguard server host
NetworkInterface: "ens33" #用于wireguard外部通信的网卡以及网络吞吐量查询的默认网卡

#测试号
# wechat:
#   appid: wx5fce1b68a48a359b
#   secret: bd89f2f41808669d4f7edea990d26ab4
#   template_id: TIPZWCT4h6hrKERN5SObzVSIS9wfW5X7pdyaVJMvBIs

#正式号
wechat:
  appid: wx34ec4a5f3f865c78
  secret: d40781dd9afcfff9bb334e10d5bb3265
  template_id: AbkrDvxHPMhtmon77tcdajRhpLgkcxJ-0nuC--cgWH4

sms:
  access_key_id: LTAI5tH1MLvjnwr2kgJDQBD7
  access_key_secret: ******************************
  sign_name: Beacon云
  sms_code: SMS_479075063

# sms:
#   access_key_id: LTAI5tM7KG3i2HMxMTbBzPN4
#   access_key_secret: ******************************
#   sign_name: 成都睿颖软件
#   sms_code: SMS_483285305
