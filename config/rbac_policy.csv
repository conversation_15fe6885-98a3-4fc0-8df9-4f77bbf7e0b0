p, superadmin, /api/*, *, allow

# enterprise
p, enterprise, /api/*, *, allow
p, enterprise, /api/enterprise/*, *, allow
p, enterprise, /api/wg/newdevice, *, deny
p, enterprise, /api/wg/bandwidth/*, *, deny
p, enterprise, /api/device/import, *, deny

# operator
p, operator, /api/device/*, *, allow
p, operator, /api/device/import, *, deny
p, operator, /api/mqtt/topics/*, GET, allow
p, operator, /api/mqtt/alarms/*, GET, allow
p, operator, /api/mqtt/publish, POST, allow
p, operator, /api/mqtt/threshold, *, allow
p, operator, /api/mqtt/threshold/*, *, allow
p, operator, /api/mqtt/thresholds, GET, allow
p, operator, /api/mqtt/alarms, GET, allow
p, operator, /api/mqtt/alarms/history, GET, allow
p, operator, /api/types, GET, allow
p, operator, /api/scada/*, *, allow
p, operator, /api/wg/*, *, deny
p, operator, /api/enterprise/*, *, deny
p, operator, /api/user/forceresetpwd/*, *, deny

# user
p, user, /api/user/updatepwd, POST, allow
p, user, /api/user/updateprofile, PUT, allow
p, user, /api/user/info, GET, allow
p, user, /api/user/forceresetpwd/*, *, deny

# role group
g, enterprise, user
g, operator, user
