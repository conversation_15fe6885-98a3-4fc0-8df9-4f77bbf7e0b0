package controller

import (
	"beacon/cloud/db"
	"beacon/cloud/service/wireguardmgr"
	"context"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreateWireguardDeviceByParams 创建WireGuard设备的核心逻辑
func CreateWireguardDeviceByParams(deviceName string, enterpriseID string) error {
	// 检查设备是否已存在
	var wgDevice wireguardmgr.Device
	err := wgMgr.DevicesColl.FindOne(context.TODO(), bson.M{"name": deviceName, "enterprise_id": enterpriseID}).Decode(&wgDevice)
	if err != mongo.ErrNoDocuments {
		return fmt.Errorf("device already exists or database error")
	}

	wgDevice.Name = deviceName
	wgDevice.EnterpriseID = enterpriseID
	wgDevice.ListenPort, _ = getNextAvailablePort()

	// 创建wireguard接口
	err = wgMgr.CreateDevice(context.TODO(), &wgDevice)
	if err != nil {
		return fmt.Errorf("failed to create wireguard interface: %v", err)
	}

	return nil
}

type deviceReq struct {
	Name           string `json:"name"`
	EnterpriseCode string `json:"enterprise_id"`
}

type peerReq struct {
	Name         string `json:"name"`
	EnterpriseID string `json:"enterprise_id"`
	DeviceName   string `json:"device_name"`
}

type bandwidthLimitReq struct {
	DeviceName string `json:"device_name" binding:"required"`
	Limit      uint32 `json:"limit" binding:"required"` // 带宽限制，单位为 kbps
}

// getNextAvailablePort finds the next available port from the database
func getNextAvailablePort() (int, error) {
	ctx := context.Background()

	// 获取所有已使用的端口，注意字段名是 listen_port 而不是 listenport
	cursor, err := db.MongoDB.Collection("wg_devices").Find(ctx, bson.M{},
		options.Find().SetProjection(bson.M{"listen_port": 1, "_id": 0}))
	if err != nil {
		return 0, fmt.Errorf("failed to query ports: %v", err)
	}
	defer cursor.Close(ctx)

	// 将所有已使用的端口放入map中，便于查找
	usedPorts := make(map[int]bool)
	var result struct {
		ListenPort int `bson:"listen_port"` // 修改字段标签以匹配数据库字段名
	}
	for cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, fmt.Errorf("failed to decode port: %v", err)
		}
		usedPorts[result.ListenPort] = true
	}

	// 如果没有任何记录，从初始端口开始
	if len(usedPorts) == 0 {
		return 50000, nil
	}

	// 从初始端口开始，找到第一个未使用的端口
	maxPort := 50000 + 10000 // 设置最大端口范围
	for port := 50000; port < maxPort; port++ {
		if !usedPorts[port] {
			return port, nil
		}
	}

	return 0, fmt.Errorf("no available ports in range %d-%d",
		50000, maxPort)
}

func CreateWireguardDevice(c *gin.Context) {
	var deviceReq deviceReq

	if err := c.ShouldBindJSON(&deviceReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	err := CreateWireguardDeviceByParams(deviceReq.Name, deviceReq.EnterpriseCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Wireguard interface created successfully"})
}

func ListDevices(c *gin.Context) {
	len, _ := strconv.Atoi(c.Param("len"))
	page, _ := strconv.Atoi(c.Param("page"))
	devices, total, err := wgMgr.ListDevices(context.TODO(), len, page)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get devices"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": devices, "total": total})
}

func GetWgDeviceStatus(c *gin.Context) {
	enterpriseID := c.Query("enterprise_id")
	if enterpriseID == "" {
		etd, _ := c.Get("enterprise_id")
		if etd == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
			return
		}
		enterpriseID = etd.(string)
	}
	log.Printf("%v\n", enterpriseID)
	var wgDevice *wireguardmgr.Device
	wgDevice, err := wgMgr.GetDevice(context.TODO(), enterpriseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get device"})
		return
	}

	peers, err := wgMgr.ListPeers(context.TODO(), enterpriseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get peers"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  wgDevice,
		"peers": peers,
	})
}

func GetWgDevicesStatus(c *gin.Context) {
	// 获取设备列表
	var deviceList struct {
		Devices []struct {
			Name         string `json:"name"`
			EnterpriseID string `json:"enterprise_id"`
		} `json:"devices"`
	}

	// 如果请求体中有设备列表，则使用提供的列表
	if c.Request.ContentLength > 0 {
		if err := c.ShouldBindJSON(&deviceList); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body format"})
			return
		}
	}

	var devices []wireguardmgr.Device

	// 获取分页的设备列表
	if len(deviceList.Devices) > 0 {
		// 构建查询条件
		conditions := make([]bson.M, len(deviceList.Devices))
		for i, device := range deviceList.Devices {
			conditions[i] = bson.M{
				"name":          device.Name,
				"enterprise_id": device.EnterpriseID,
			}
		}

		// 查询指定的设备
		cursor, err := wgMgr.DevicesColl.Find(
			context.TODO(),
			bson.M{"$or": conditions},
		)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get devices"})
			return
		}
		defer cursor.Close(context.TODO())

		if err := cursor.All(context.TODO(), &devices); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to decode devices"})
			return
		}
	} else {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No devices specified"})
		return
	}

	// 获取每个设备的带宽信息
	var devicesWithBandwidth []map[string]any
	for _, device := range devices {
		bandwidth, err := wgMgr.GetWgInterfaceBandwidth(device.Name)
		if err != nil {
			log.Printf("Warning: failed to get bandwidth for device %s: %v", device.Name, err)
			continue
		}
		devicesWithBandwidth = append(devicesWithBandwidth, bandwidth)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": devicesWithBandwidth,
	})
}

func DeleteWgDevice(c *gin.Context) {
	var deviceReq deviceReq
	if err := c.ShouldBindJSON(&deviceReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Get user info from context
	userRole := c.MustGet("role").(string)

	// If not admin, verify enterprise code matches
	if userRole != roleAdmin && userRole != roleSuperAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "No permission to delete device from this enterprise"})
		return
	}

	// For admin, enterprise code check is skipped
	err := wgMgr.DeleteDevice(context.TODO(), deviceReq.Name, deviceReq.EnterpriseCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Wireguard interface deleted successfully"})
}

// 创建 Peer
func NewWgDevicePeer(c *gin.Context) {
	var req peerReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	//check if device is exist
	var wgDevice wireguardmgr.Device
	err := wgMgr.DevicesColl.FindOne(context.TODO(), bson.M{"name": req.DeviceName, "enterprise_id": req.EnterpriseID}).Decode(&wgDevice)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get device"})
		return
	}

	peer, err := wgMgr.CreatePeer(context.TODO(), req.DeviceName, req.EnterpriseID, req.Name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to create peer: %v", err),
		})
		return
	}

	// 生成客户端配置
	// device, err := wgMgr.GetDevice(context.TODO(), req.DeviceName, req.EnterpriseID)
	// if err != nil {
	// 	c.JSON(http.StatusInternalServerError, gin.H{
	// 		"error": fmt.Sprintf("Failed to get device: %v", err),
	// 	})
	// 	return
	// }

	clientConfig, err := wgMgr.GeneratePeerConfig(peer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to generate client config: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Peer created successfully",
		"peer":    peer,
		"config":  clientConfig,
	})
}

func DeleteWgDevicePeer(c *gin.Context) {
	var req struct {
		DeviceName   string `json:"device_name" binding:"required"`
		EnterpriseID string `json:"enterprise_id" binding:"required"`
		PeerID       string `json:"peer_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	peerID, err := primitive.ObjectIDFromHex(req.PeerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid peer ID"})
		return
	}
	err = wgMgr.DeletePeer(context.TODO(), req.DeviceName, req.EnterpriseID, peerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to delete peer"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Peer deleted successfully"})
}

func GetWgPeerQRCode(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	peerIDStr := c.Query("peer_id")
	if enterpriseID == "" || peerIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	peerID, err := primitive.ObjectIDFromHex(peerIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid peer ID"})
		return
	}
	peer, err := wgMgr.GetPeer(context.TODO(), enterpriseID.(string), peerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get peer"})
		return
	}
	data, err := wgMgr.GeneratePeerConfig(peer)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to generate peer config"})
		return
	}
	svg, err := wgMgr.Helper.GeneratePeerQRCodeBase64(data)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to generate QR code"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"svg": svg})
}

// 获取单个设备信息
// func GetWgDevice(c *gin.Context) {
// 	enterpriseID,_ := c.Get("enterprise_id")

// 	if enterpriseID == "" {
// 		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
// 		return
// 	}

// 	device, err := wgMgr.GetDevice(context.TODO(), enterpriseID.(string))
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get device: %v", err)})
// 		return
// 	}

// 	c.JSON(http.StatusOK, gin.H{"data": device})
// }

// Get peer configuration
func GetPeerConfig(c *gin.Context) {
	deviceName := c.Query("device_name")
	enterpriseID, _ := c.Get("enterprise_id")
	peerIDStr := c.Query("peer_id")

	if deviceName == "" || enterpriseID == "" || peerIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name, enterprise ID and peer ID cannot be empty"})
		return
	}

	peerID, err := primitive.ObjectIDFromHex(peerIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid peer ID"})
		return
	}

	peer, err := wgMgr.GetPeer(context.TODO(), enterpriseID.(string), peerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get peer: %v", err)})
		return
	}

	config, err := wgMgr.GeneratePeerConfig(peer)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to generate configuration: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"config": config})
}

// Start WireGuard interface
func StartWgInterface(c *gin.Context) {
	deviceName := c.Query("device_name")
	enterpriseID := c.Query("enterprise_id")
	if deviceName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name cannot be empty"})
		return
	}
	if enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID cannot be empty"})
		return
	}

	// Get device to find namespace
	device, err := wgMgr.GetDevice(context.TODO(), enterpriseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get device: %v", err)})
		return
	}

	err = wgMgr.Helper.StartInterface(deviceName, device.Namespace)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to start interface: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Interface started successfully"})
}

// Stop WireGuard interface
func StopWgInterface(c *gin.Context) {
	deviceName := c.Query("device_name")
	enterpriseID := c.Query("enterprise_id")
	if deviceName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name cannot be empty"})
		return
	}
	if enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID cannot be empty"})
		return
	}

	// Get device to find namespace
	device, err := wgMgr.GetDevice(context.TODO(), enterpriseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get device: %v", err)})
		return
	}

	err = wgMgr.Helper.StopInterface(deviceName, device.Namespace)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to stop interface: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Interface stopped successfully"})
}

func GetWgInterfaceStatus(c *gin.Context) {
	deviceName := c.Query("device_name")
	if deviceName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name cannot be empty"})
		return
	}

	status, err := wgMgr.GetWGPeerStatus(deviceName)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get interface status: %v", err)})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": status})
}

func GetWgStats(c *gin.Context) {
	devicename := c.Query("device_name")
	time_range := c.Query("time_range")

	if devicename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name cannot be empty"})
		return
	}

	stats, err := wireguardmgr.QueryDeviceStats(devicename, time_range, 0)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get stats"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// SetWgBandwidthLimit 设置 WireGuard 接口的带宽限制
func SetWgBandwidthLimit(c *gin.Context) {
	var req bandwidthLimitReq
	enterprise_id := c.GetString("enterprise_id")
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	err := wgMgr.SetDeviceBandwidthLimit(context.TODO(), req.DeviceName, enterprise_id, req.Limit)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to set bandwidth limit: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Bandwidth limit set successfully"})
}

// RemoveWgBandwidthLimit 移除 WireGuard 接口的带宽限制
func RemoveWgBandwidthLimit(c *gin.Context) {
	deviceName := c.Query("device_name")
	enterpriseID, _ := c.Get("enterprise_id")

	if deviceName == "" || enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name and enterprise ID are required"})
		return
	}

	err := wgMgr.RemoveDeviceBandwidthLimit(context.TODO(), deviceName, enterpriseID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to remove bandwidth limit: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Bandwidth limit removed successfully"})
}

// GetWgBandwidthLimit 获取 WireGuard 接口的带宽限制
func GetWgBandwidthLimit(c *gin.Context) {
	deviceName := c.Query("device_name")
	enterpriseID, _ := c.Get("enterprise_id")

	if deviceName == "" || enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name and enterprise ID are required"})
		return
	}

	limit, err := wgMgr.GetDeviceBandwidthLimit(context.TODO(), deviceName, enterpriseID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get bandwidth limit: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"limit": limit})
}

// ListWgBandwidthLimits 列出所有接口的带宽限制
func ListWgBandwidthLimits(c *gin.Context) {
	limits, err := wgMgr.ListAllRateLimits(context.TODO())
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to list bandwidth limits: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"limits": limits})
}

// RestoreWgBandwidthLimits 恢复所有存储的带宽限制
func RestoreWgBandwidthLimits(c *gin.Context) {
	err := wgMgr.EnsureAllRateLimitsApplied(context.TODO())
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to restore bandwidth limits: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Bandwidth limits restored successfully"})
}

// GetPeerAllowedIPs retrieves the server-side AllowedIPs list for a specific peer
func GetPeerAllowedIPs(c *gin.Context) {
	deviceName := c.Query("device_name")
	enterpriseID, _ := c.Get("enterprise_id")
	peerIDStr := c.Query("peer_id")

	if deviceName == "" || enterpriseID == "" || peerIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device name, enterprise ID and peer ID cannot be empty"})
		return
	}

	peerID, err := primitive.ObjectIDFromHex(peerIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid peer ID"})
		return
	}

	// 获取服务器端的peer路由配置
	routes, err := wgMgr.GetServerPeerRoutes(context.TODO(), enterpriseID.(string), peerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get peer routes: %v", err)})
		return
	}

	// 返回服务器端的路由配置
	c.JSON(http.StatusOK, gin.H{"allowed_ips": routes})
}

// SavePeerAllowedIPs updates the AllowedIPs list for a specific peer
func SavePeerAllowedIPs(c *gin.Context) {
	var req struct {
		DeviceName   string   `json:"device_name" binding:"required"`
		EnterpriseID string   `json:"enterprise_id" binding:"required"`
		PeerID       string   `json:"peer_id" binding:"required"`
		AllowedIPs   []string `json:"allowed_ips" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	peerID, err := primitive.ObjectIDFromHex(req.PeerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid peer ID"})
		return
	}

	// 更新服务器端路由配置
	err = wgMgr.UpdatePeerAllowedIPs(context.TODO(), req.DeviceName, req.EnterpriseID, peerID, req.AllowedIPs)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to update allowed IPs: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Peer allowed IPs updated successfully"})
}

// UpdatePeerAllowedIPsPublic updates the AllowedIPs list for a specific peer without requiring authentication
// This API is specifically designed for hardware devices to push their sub-device IPs
func UpdatePeerAllowedIPsPublic(c *gin.Context) {
	var req struct {
		PeerID     string   `json:"peer_id" binding:"required"`
		AllowedIPs []string `json:"allowed_ips" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	peerID, err := primitive.ObjectIDFromHex(req.PeerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid peer ID"})
		return
	}

	// 查找peer信息，不使用enterpriseID过滤
	var peer wireguardmgr.Peer
	err = wgMgr.PeersColl.FindOne(context.TODO(), bson.M{"_id": peerID}).Decode(&peer)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to find peer: %v", err)})
		return
	}

	// 获取peer的企业ID和设备名称
	enterpriseID := peer.EnterpriseID
	var device wireguardmgr.Device
	err = wgMgr.DevicesColl.FindOne(context.TODO(), bson.M{"enterprise_id": enterpriseID}).Decode(&device)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get device: %v", err)})
		return
	}

	// 使用现有方法更新服务器端路由配置
	err = wgMgr.UpdatePeerAllowedIPs(context.TODO(), device.Name, enterpriseID, peerID, req.AllowedIPs)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to update allowed IPs: %v", err)})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Peer allowed IPs updated successfully"})
}
