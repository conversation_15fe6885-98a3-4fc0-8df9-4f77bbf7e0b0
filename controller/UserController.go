package controller

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"

	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/notification"
	"beacon/cloud/utils"

	"bytes"
	"encoding/base64"
	"image"

	"github.com/chai2010/webp"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
)

const (
	maxUsernameLength    = 12
	usernamePattern      = "^[a-zA-Z][a-zA-Z0-9]*$"
	minPasswordLength    = 8
	maxPasswordLength    = 20
	passwordSpecialChars = "@$!%*?&"
)

// 添加邮箱验证函数
func isValidEmail(email string) bool {
	// 基本的邮箱格式验证
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// 检查邮箱是否已存在
func emailExists(email string) (bool, error) {
	email = strings.ToLower(email)
	filter := bson.M{"email": email}
	err := db.MongoDB.Collection("users").FindOne(context.TODO(), filter).Err()
	if err == mongo.ErrNoDocuments {
		return false, nil
	}
	return err == nil, err
}

// 检查用户名是否已存在
func usernameExists(username string) (bool, error) {
	filter := bson.M{"username": username}
	err := db.MongoDB.Collection("users").FindOne(context.TODO(), filter).Err()
	if err == mongo.ErrNoDocuments {
		return false, nil
	}
	return err == nil, err
}

func isValidUsername(username string) bool {
	if len(username) > maxUsernameLength {
		return false
	}
	return regexp.MustCompile(usernamePattern).MatchString(username)
}

func isValidPassword(password string) bool {
	if len(password) < minPasswordLength || len(password) > maxPasswordLength {
		return false
	}

	patterns := []string{
		`[a-zA-Z]`,
		`[0-9]`,
		fmt.Sprintf(`[%s]`, regexp.QuoteMeta(passwordSpecialChars)),
	}

	count := 0
	for _, pattern := range patterns {
		if regexp.MustCompile(pattern).MatchString(password) {
			count++
		}
	}
	return count >= 2
}

/* ------------------------------ Helper Functions ----------------------------- */
func RenderPasswordTemplateAndSendEmail(c *gin.Context, email string, newPassword string) {
	subject := fmt.Sprintf("%s - New Password", email)
	body, err := notification.RenderTemplate(notification.ResetPasswordTemplate, models.EmailData{
		Content: newPassword,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to render password email template"})
		return
	}
	to := []string{email}
	if err := notification.SendEmail(to, subject, body); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send password email"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "New password sent to email: " + email})
}

func prepareUser(user *models.User) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("password hashing failed")
	}

	user.Password = string(hashedPassword)
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now
	return nil
}

func handleEnterpriseRegistration(c *gin.Context, user *models.User) bool {
	enterpriseCode := c.Query("enterprise_code")
	if enterpriseCode == "" {
		return false
	}

	if !VerifyCode(enterpriseCode) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enterprise code"})
		return false
	}

	if err := MarkCodeAsUsed(enterpriseCode); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Enterprise registration failed"})
		return false
	}

	user.Role = "enterprise"
	enterpriseID, _ := utils.GenerateRandomString(16)
	user.EnterpriseID = enterpriseID
	return true
}

/* ---------------------------- Registration & Auth ---------------------------- */
func Register(c *gin.Context) {
	var user models.User
	if err := c.ShouldBindJSON(&user); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	user.Email = strings.ToLower(user.Email)
	if user.Username == "" || user.Password == "" || user.Email == "" || user.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Username, name, password, and email are required"})
		return
	}

	if !isValidUsername(user.Username) {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf(
			"Username must start with a letter, contain alphanumerics only, and be <= %d characters",
			maxUsernameLength,
		)})
		return
	}

	if !isValidPassword(user.Password) {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf(
			"Password must be %d-%d chars with at least two character types from: letters, numbers, specials (%s)",
			minPasswordLength, maxPasswordLength, passwordSpecialChars,
		)})
		return
	}

	if exists, _ := emailExists(user.Email); exists {
		c.JSON(http.StatusConflict, gin.H{"error": "Email already registered"})
		return
	}

	if exists, _ := usernameExists(user.Username); exists {
		c.JSON(http.StatusConflict, gin.H{"error": "Username unavailable"})
		return
	}

	if !VerifyVerificationCode(user.Email, c.Query("code")) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid verification code"})
		return
	}
	DeleteVerificationCode(c.Query("code"))

	isEnterprise := false
	if !handleEnterpriseRegistration(c, &user) {
		user.Role = "user"
	} else {
		isEnterprise = true
	}

	if err := prepareUser(&user); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if _, err := userCol.InsertOne(c, user); err != nil {
		log.Printf("Registration error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "User registration failed"})
		return
	}

	// 为企业用户自动创建WireGuard设备
	if isEnterprise && user.EnterpriseID != "" {
		deviceName := "wg-" + user.Username
		err := CreateWireguardDeviceByParams(deviceName, user.EnterpriseID)
		if err != nil {
			log.Printf("WireGuard device creation for user %s failed: %v", user.Username, err)
			// 不影响用户注册结果，只记录日志
		} else {
			log.Printf("WireGuard device created for enterprise user: %s", user.Username)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Registration successful",
		"username": user.Username,
		"name":     user.Name,
		"email":    user.Email,
		"role":     user.Role,
	})
}

func Login(c *gin.Context) {
	var credentials struct {
		Identifier string `json:"username"`
		Password   string `json:"password"`
	}

	if err := c.ShouldBindJSON(&credentials); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid credentials format"})
		return
	}

	filter := bson.M{
		"$or": []bson.M{
			{"username": credentials.Identifier},
			{"email": credentials.Identifier},
		},
	}

	var user models.User
	if err := userCol.FindOne(c, filter).Decode(&user); err != nil {
		log.Printf("Login attempt for %s: %v", credentials.Identifier, err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(credentials.Password)); err != nil {
		log.Printf("Failed password attempt for user: %s", user.Username)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	token, err := utils.GenerateToken(user.Username, user.EnterpriseID)
	if err != nil {
		log.Printf("Token generation failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Authentication system error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"username":      user.Username,
		"name":          user.Name,
		"email":         user.Email,
		"role":          user.Role,
		"token":         token,
		"enterprise_id": user.EnterpriseID,
		"avatar":        user.Avatar,
	})
}

/* ----------------------------- Password Management ---------------------------- */
func resetPassword(email string) (string, error) {
	newPassword, err := utils.GenerateRandomString(12)
	if err != nil {
		return "", fmt.Errorf("password hashing failed")
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("password hashing failed")
	}

	_, err = userCol.UpdateOne(
		context.TODO(),
		bson.M{"email": email},
		bson.M{"$set": bson.M{"password": hashedPassword}},
	)
	return newPassword, err
}

func ForgotPassword(c *gin.Context) {
	email := strings.ToLower(c.Query("email"))
	if exists, _ := emailExists(email); !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Email not registered"})
		return
	}

	sendVerificationCode(c, email)
}

func ResetPassword(c *gin.Context) {
	email := strings.ToLower(c.Query("email"))
	code := c.Query("code")

	if !VerifyVerificationCode(email, code) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid verification code"})
		return
	}
	DeleteVerificationCode(code)

	newPassword, err := resetPassword(email)
	if err != nil {
		log.Printf("Password reset failed for %s: %v", email, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Password reset failed"})
		return
	}

	RenderPasswordTemplateAndSendEmail(c, email, newPassword)
}

func UpdatePassword(c *gin.Context) {
	username := c.MustGet("username").(string)
	var req struct {
		Old string `json:"old_password"`
		New string `json:"new_password"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	var user models.User
	if err := userCol.FindOne(c, bson.M{"username": username}).Decode(&user); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Old)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Current password mismatch"})
		return
	}

	if !isValidPassword(req.New) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "New password does not meet requirements"})
		return
	}

	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(req.New), bcrypt.DefaultCost)
	_, err := userCol.UpdateOne(
		c,
		bson.M{"_id": user.ID},
		bson.M{"$set": bson.M{"password": hashedPassword}},
	)

	if err != nil {
		log.Printf("Password update failed for %s: %v", username, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Password update failed"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password updated successfully"})
}

/* ------------------------------ Profile Management ----------------------------- */
func UpdateProfile(c *gin.Context) {
	username := c.MustGet("username").(string)

	// 处理验证码发送
	if mode := c.Query("mode"); mode == "verify" {
		email := strings.ToLower(c.Query("email"))
		if exists, _ := emailExists(email); exists {
			c.JSON(http.StatusConflict, gin.H{"error": "Email already registered"})
			return
		}
		sendVerificationCode(c, email)
		return
	}

	// 处理资料更新
	var req struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// 准备更新字段
	updateFields := bson.M{}

	// 处理名称更新
	if req.Name != "" {
		updateFields["name"] = req.Name
	}

	// 处理邮箱更新
	if req.Email != "" {
		req.Email = strings.ToLower(req.Email)

		// 验证邮箱格式
		if !isValidEmail(req.Email) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email format"})
			return
		}

		// 检查验证码
		code := c.Query("code")
		if code != "" {
			if !VerifyVerificationCode(req.Email, code) {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid verification code"})
				return
			}
			DeleteVerificationCode(code)
			updateFields["email"] = req.Email
		}
	}

	// 如果没有要更新的字段，直接返回
	if len(updateFields) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// 执行更新
	_, err := userCol.UpdateOne(
		c,
		bson.M{"username": username},
		bson.M{"$set": updateFields},
	)

	if err != nil {
		log.Printf("Profile update failed for %s: %v", username, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Profile update failed"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Profile updated successfully",
		"updated_fields": updateFields,
	})
}

func UpdateAvatar(c *gin.Context) {
	username := c.MustGet("username").(string)
	var req struct {
		Avatar string `json:"avatar"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}
	if len(req.Avatar) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Avatar is required"})
		return
	}

	// 解码base64
	imgData, err := decodeBase64Image(req.Avatar)
	if err != nil {
		log.Printf("Avatar base64 decode failed: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image data"})
		return
	}

	// 尝试压缩图像，如果失败则使用原始图像数据
	webpData, err := compressToWebP(imgData)
	if err != nil {
		log.Printf("WebP compression failed: %v, using original image format", err)
		// 如果压缩失败，尝试使用原始图像数据
		avatarBase64 := req.Avatar
		// 确保base64字符串有正确前缀
		if !strings.Contains(avatarBase64, ";base64,") {
			avatarBase64 = "data:image/jpeg;base64," + avatarBase64
		}

		_, err = userCol.UpdateOne(
			c,
			bson.M{"username": username},
			bson.M{"$set": bson.M{"avatar": avatarBase64}},
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Avatar update failed"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "Avatar updated successfully", "avatar": avatarBase64})
		return
	}

	// 压缩成功，转回base64
	avatarBase64 := encodeToBase64(webpData)
	_, err = userCol.UpdateOne(
		c,
		bson.M{"username": username},
		bson.M{"$set": bson.M{"avatar": avatarBase64}},
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Avatar update failed"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Avatar updated successfully", "avatar": avatarBase64})
}

func decodeBase64Image(data string) ([]byte, error) {
	// 找到base64数据部分
	idx := strings.Index(data, ";base64,")
	if idx != -1 {
		data = data[idx+8:]
	} else {
		// 尝试寻找其他类型的base64标记
		idx = strings.Index(data, ",")
		if idx != -1 {
			data = data[idx+1:]
		}
	}
	return base64.StdEncoding.DecodeString(data)
}

func compressToWebP(imgData []byte) ([]byte, error) {
	// 尝试解码图像
	img, format, err := image.Decode(bytes.NewReader(imgData))
	if err != nil {
		return nil, fmt.Errorf("image decode failed (format: unknown): %v", err)
	}

	log.Printf("Successfully decoded image format: %s", format)

	buf := new(bytes.Buffer)
	// 使用适中的压缩质量，提高兼容性
	options := &webp.Options{Lossless: false, Quality: 80}
	if err := webp.Encode(buf, img, options); err != nil {
		return nil, fmt.Errorf("webp encoding failed: %v", err)
	}

	return buf.Bytes(), nil
}

func encodeToBase64(data []byte) string {
	return "data:image/webp;base64," + base64.StdEncoding.EncodeToString(data)
}

func validateAndPrepareUser(user *models.User) error {
	// Validate username
	if !isValidUsername(user.Username) {
		return fmt.Errorf("username must start with a letter, contain only letters and numbers, and be no longer than %d characters", maxUsernameLength)
	}

	// Validate password
	if !isValidPassword(user.Password) {
		return fmt.Errorf("password must be %d-%d characters long and contain at least two types of the following: letters, numbers, special characters (%s)",
			minPasswordLength, maxPasswordLength, passwordSpecialChars)
	}

	// Check if username exists
	if exists, _ := usernameExists(user.Username); exists {
		return fmt.Errorf("username already exists")
	}

	// Check if email exists
	if exists, _ := emailExists(user.Email); exists {
		return fmt.Errorf("email already exists")
	}

	// Hash password and set timestamps
	return prepareUser(user)
}
