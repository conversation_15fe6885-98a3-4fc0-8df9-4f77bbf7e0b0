# WireGuard Namespace Implementation Testing Guide

## Prerequisites

Before testing the namespace implementation, ensure you have:

1. **Root privileges** - Network namespace operations require root access
2. **WireGuard tools installed** - `wg`, `wg-quick`, and `ip` commands
3. **iproute2 package** - For `ip netns` commands
4. **Running MongoDB and TDengine** - For database operations
5. **IP forwarding enabled** - Required for namespace connectivity
6. **Correct network interface configured** - Update config.yaml with your interface name

## System Verification

Run the verification script to check your system setup:

```bash
sudo ./scripts/verify-network-setup.sh
```

This script will check:
- Root privileges
- Required tools installation
- IP forwarding configuration
- iptables functionality
- Network namespace support
- WireGuard kernel module
- Network interface availability

## Installation Commands

```bash
# Install WireGuard (Ubuntu/Debian)
sudo apt update
sudo apt install wireguard-tools iproute2 iptables

# Install WireGuard (CentOS/RHEL)
sudo yum install epel-release
sudo yum install wireguard-tools iproute2 iptables

# Enable IP forwarding
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward

# Make IP forwarding permanent
echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Configuration Setup

Update your configuration file with the correct network interface:

```yaml
# config/config.yaml
NetworkInterface: "eth0"  # Replace with your actual interface name
```

Find your interface name:
```bash
ip link show
```

## Testing Steps

### 1. Basic Functionality Test

#### Create a WireGuard Device
```bash
curl -X POST http://localhost:8080/api/wireguard/device \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-device",
    "enterprise_code": "test-enterprise"
  }'
```

#### Verify Namespace Creation
```bash
# List all network namespaces
sudo ip netns list

# Should show: wg-test-enterprise-test-device
```

#### Check Device Status
```bash
curl "http://localhost:8080/api/wireguard/device/status?enterprise_id=test-enterprise"
```

### 2. Namespace Isolation Test

#### Create Multiple Devices
```bash
# Create device for enterprise A
curl -X POST http://localhost:8080/api/wireguard/device \
  -H "Content-Type: application/json" \
  -d '{
    "name": "device-a",
    "enterprise_code": "enterprise-a"
  }'

# Create device for enterprise B
curl -X POST http://localhost:8080/api/wireguard/device \
  -H "Content-Type: application/json" \
  -d '{
    "name": "device-b",
    "enterprise_code": "enterprise-b"
  }'
```

#### Verify Isolation
```bash
# List namespaces - should show both
sudo ip netns list

# Check interfaces in each namespace
sudo ip netns exec wg-enterprise-a-device-a wg show
sudo ip netns exec wg-enterprise-b-device-b wg show

# Verify they can't see each other's interfaces
sudo ip netns exec wg-enterprise-a-device-a wg show device-b  # Should fail
sudo ip netns exec wg-enterprise-b-device-b wg show device-a  # Should fail
```

### 3. Interface Management Test

#### Start Interface
```bash
curl "http://localhost:8080/api/wireguard/start?device_name=test-device&enterprise_id=test-enterprise"
```

#### Verify Interface is Running
```bash
# Check in namespace
sudo ip netns exec wg-test-enterprise-test-device wg show test-device

# Check interface status via API
curl "http://localhost:8080/api/wireguard/device/status?enterprise_id=test-enterprise"
```

#### Stop Interface
```bash
curl "http://localhost:8080/api/wireguard/stop?device_name=test-device&enterprise_id=test-enterprise"
```

### 4. Peer Management Test

#### Create a Peer
```bash
curl -X POST http://localhost:8080/api/wireguard/peer \
  -H "Content-Type: application/json" \
  -d '{
    "device_name": "test-device",
    "enterprise_code": "test-enterprise",
    "peer_name": "test-peer"
  }'
```

#### Verify Peer in Namespace
```bash
# Check peer configuration
sudo ip netns exec wg-test-enterprise-test-device wg show test-device peers

# Get peer status via API
curl "http://localhost:8080/api/wireguard/peer/status?interface=test-device"
```

### 5. Statistics Collection Test

#### Start Statistics Collector
The collector should automatically start with the application.

#### Verify Data Collection
```bash
# Check if statistics are being collected
# (This depends on your TDengine setup)

# Check bandwidth via API
curl "http://localhost:8080/api/wireguard/bandwidth?interface=test-device"
```

### 6. Network Connectivity Test

#### Verify Namespace Networking
```bash
# Check veth pair creation
ip link show | grep veth-h

# Check namespace connectivity
sudo ip netns exec wg-test-enterprise-test-device ping -c 3 ***********

# Check iptables rules
sudo iptables -t nat -L PREROUTING -n -v | grep {listen_port}
sudo iptables -t nat -L POSTROUTING -n -v | grep MASQUERADE
```

#### Test External Connectivity
```bash
# Test UDP port accessibility (replace with actual port and IP)
nc -u {server_ip} {listen_port}

# Check if WireGuard is listening in namespace
sudo ip netns exec wg-test-enterprise-test-device ss -ulnp | grep {listen_port}
```

### 7. Cleanup Test

#### Delete Device
```bash
curl -X DELETE "http://localhost:8080/api/wireguard/device?name=test-device&enterprise_id=test-enterprise"
```

#### Verify Complete Cleanup
```bash
# Namespace should be removed
sudo ip netns list | grep wg-test-enterprise-test-device  # Should return nothing

# Veth interfaces should be removed
ip link show | grep veth-h-test-enter  # Should return nothing

# Iptables rules should be removed
sudo iptables -t nat -L PREROUTING -n -v | grep {listen_port}  # Should return nothing
```

## Manual Namespace Testing

### Direct Namespace Commands

```bash
# Create namespace manually
sudo ip netns add test-namespace

# Execute commands in namespace
sudo ip netns exec test-namespace ip link show

# Delete namespace
sudo ip netns delete test-namespace
```

### WireGuard in Namespace

```bash
# Create a test namespace
sudo ip netns add wg-test

# Set up loopback
sudo ip netns exec wg-test ip link set lo up

# Create WireGuard interface in namespace
sudo ip netns exec wg-test ip link add wg0 type wireguard

# Configure and start
sudo ip netns exec wg-test wg-quick up /etc/wireguard/test.conf
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure running with root privileges
   sudo ./your-application
   ```

2. **Namespace Already Exists**
   ```bash
   # Clean up existing namespaces
   sudo ip netns delete wg-enterprise-device
   ```

3. **WireGuard Tools Not Found**
   ```bash
   # Install WireGuard tools
   sudo apt install wireguard-tools
   ```

4. **Interface Not Starting**
   ```bash
   # Check configuration file
   sudo cat /etc/wireguard/device-name.conf
   
   # Check namespace
   sudo ip netns exec namespace-name wg show
   ```

### Debug Commands

```bash
# List all namespaces
sudo ip netns list

# Show interfaces in namespace
sudo ip netns exec namespace-name ip link show

# Show WireGuard status in namespace
sudo ip netns exec namespace-name wg show

# Check application logs
tail -f /var/log/your-application.log
```

## Expected Results

After successful implementation:

1. **Isolation**: Each enterprise's WireGuard instance runs in its own namespace
2. **No Conflicts**: Multiple instances can use the same port numbers
3. **Clean Separation**: Interfaces from different enterprises cannot see each other
4. **Proper Cleanup**: Namespaces are automatically created and destroyed with devices
5. **Statistics Collection**: Data is collected from namespace-isolated interfaces

## Performance Testing

### Load Testing
```bash
# Create multiple devices simultaneously
for i in {1..10}; do
  curl -X POST http://localhost:8080/api/wireguard/device \
    -H "Content-Type: application/json" \
    -d "{\"name\": \"device-$i\", \"enterprise_code\": \"enterprise-$i\"}" &
done
wait

# Verify all namespaces created
sudo ip netns list | wc -l  # Should show 10+ namespaces
```

### Memory Usage
```bash
# Monitor memory usage
top -p $(pgrep your-application)

# Check namespace resource usage
sudo ip netns exec namespace-name ss -tuln
```

This testing guide ensures comprehensive validation of the namespace implementation.
