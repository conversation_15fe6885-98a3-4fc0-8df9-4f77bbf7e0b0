package middleware

import (
	"log"
	"net/http"
	"path/filepath"
	"sync"

	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
)

var (
	enforcer *casbin.Enforcer
	once     sync.Once
	mutex    sync.RWMutex // 添加读写锁保护并发访问
)

// InitCasbin initializes the Casbin enforcer
func InitCasbin() (*casbin.Enforcer, error) {
	var err error
	once.Do(func() {
		modelPath := filepath.Join("config", "rbac_model.conf")
		policyPath := filepath.Join("config", "rbac_policy.csv")
		enforcer, err = casbin.NewEnforcer(modelPath, policyPath)
		if err != nil {
			log.Printf("Failed to initialize Casbin: %v", err)
			return
		}
		// Load policies from DB or file
		err = enforcer.LoadPolicy()
		if err != nil {
			log.Printf("Failed to load Casbin policy: %v", err)
			return
		}
	})
	return enforcer, err
}

// ReloadPolicy reloads the Casbin policy from the storage
func ReloadPolicy() error {
	mutex.Lock()
	defer mutex.Unlock()

	if enforcer == nil {
		_, err := InitCasbin()
		return err
	}

	err := enforcer.LoadPolicy()
	if err != nil {
		log.Printf("Failed to reload Casbin policy: %v", err)
		return err
	}

	log.Printf("Casbin policy reloaded successfully")
	return nil
}

// GetEnforcer returns the current Casbin enforcer
func GetEnforcer() *casbin.Enforcer {
	mutex.RLock()
	defer mutex.RUnlock()
	return enforcer
}

// Authorize determines if current subject has been authorized to take an action on an object
func Authorize() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get current user role from JWT token
		role, exists := c.Get("role")
		if !exists {
			log.Printf("Role not found in context")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required",
			})
			c.Abort()
			return
		}

		roleStr, ok := role.(string)
		if !ok {
			log.Printf("Role is not a string: %v", role)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid role format",
			})
			c.Abort()
			return
		}

		// Get request path and method
		obj := c.Request.URL.Path
		act := c.Request.Method

		mutex.RLock()
		allowed, err := enforcer.Enforce(roleStr, obj, act)
		mutex.RUnlock()

		if err != nil {
			log.Printf("Casbin enforce error: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Authorization system error",
			})
			c.Abort()
			return
		}

		if !allowed {
			log.Printf("Permission denied: role=%s, path=%s, method=%s", roleStr, obj, act)
			c.JSON(http.StatusForbidden, gin.H{
				"error":  "Permission denied",
				"role":   roleStr,
				"path":   obj,
				"method": act,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
