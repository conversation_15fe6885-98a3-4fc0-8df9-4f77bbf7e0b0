package models

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DeviceType represents a device type
type DeviceType struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Name      string             `json:"name" bson:"name"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
}

var (
	ErrTypeNotFound  = errors.New("device type not found")
	ErrDuplicateType = errors.New("duplicate device type")
	ErrTypeInUse     = errors.New("device type is in use by devices")
)

// CreateDeviceType creates a new device type
func CreateDeviceType(name string, collection *mongo.Collection) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	deviceType := DeviceType{
		ID:        primitive.NewObjectID(),
		Name:      name,
		CreatedAt: time.Now(),
	}

	_, err := collection.InsertOne(ctx, deviceType)
	if mongo.IsDuplicateKeyError(err) {
		return ErrDuplicateType
	}
	return err
}

// GetDeviceTypes retrieves all device types
func GetDeviceTypes(collection *mongo.Collection) ([]DeviceType, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	opts := options.Find().SetSort(bson.D{{Key: "name", Value: 1}})
	cursor, err := collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var deviceTypes []DeviceType
	if err := cursor.All(ctx, &deviceTypes); err != nil {
		return nil, err
	}

	return deviceTypes, nil
}

// DeleteDeviceType deletes a device type
func DeleteDeviceType(name string, typeCol, deviceCol *mongo.Collection) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if the type is in use
	count, err := deviceCol.CountDocuments(ctx, bson.M{"type": name})
	if err != nil {
		return err
	}
	if count > 0 {
		return ErrTypeInUse
	}

	// Delete the type
	result, err := typeCol.DeleteOne(ctx, bson.M{"name": name})
	if err != nil {
		return err
	}
	if result.DeletedCount == 0 {
		return ErrTypeNotFound
	}

	return nil
}

// GetTypeUsageCount returns the number of devices using a specific type
func GetTypeUsageCount(name string, deviceCol *mongo.Collection) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	count, err := deviceCol.CountDocuments(ctx, bson.M{"type": name})
	if err != nil {
		return 0, err
	}

	return count, nil
}

// DeviceTypeExists checks if a device type with the given name exists
func DeviceTypeExists(name string, collection *mongo.Collection) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	count, err := collection.CountDocuments(ctx, bson.M{"name": name})
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
