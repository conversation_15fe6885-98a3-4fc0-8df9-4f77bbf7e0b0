package models

import "time"

// Topic 结构体定义
type Topic struct {
	ID               string    `json:"id" bson:"_id"`
	Topic            string    `json:"topic" bson:"topic"`
	LastUpdated      time.Time `json:"last_updated" bson:"last_updated"`
	SubscribersCount int       `json:"subscribers_count,omitempty" bson:"subscribers_count,omitempty"`
	CreatedBy        string    `json:"created_by" bson:"created_by"`       // 创建者
	EnterpriseID     string    `json:"enterprise_id" bson:"enterprise_id"` // 创建者的 ClientID
	DeviceSN         string    `json:"device_sn" bson:"device_sn"`         // 绑定的设备 SN
	CreatedAt        time.Time `json:"created_at" bson:"created_at"`
	DataType         string    `json:"data_type" bson:"data_type"` // 数据类型: number, string, boolean, json, etc.
}

// ThresholdRule 定义阈值规则
type ThresholdRule struct {
	ID            string    `json:"id" bson:"_id"`
	EnterpriseID  string    `json:"enterprise_id" bson:"enterprise_id"`
	Mode          string    `json:"mode" bson:"mode"`       // 模式: email, phone, wechat
	Contact       string    `json:"contact" bson:"contact"` // 联系方式: email, phone, or wechat openid
	Topic         string    `json:"topic" bson:"topic"`
	Expression    string    `json:"expression" bson:"expression"` // 阈值表达式，如 "value > 100" 或 "value == 50"
	CreatedBy     string    `json:"created_by" bson:"created_by"`
	CreatedAt     time.Time `json:"created_at" bson:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" bson:"updated_at"`
	Description   string    `json:"description" bson:"description"`
	IsEnabled     bool      `json:"is_enabled" bson:"is_enabled"`
	AlertCount    int       `json:"alert_count" bson:"alert_count"`
	LastAlertTime time.Time `json:"last_alert_time" bson:"last_alert_time"`
}

// Message 结构体定义
type Message struct {
	Ts       time.Time `json:"time"`
	ClientID string    `json:"client_id"`
	Topic    string    `json:"topic"`
	Payload  string    `json:"payload"`
	QoS      byte      `json:"qos"`
	Username string    `json:"username"`
	IsAlarm  bool      `json:"is_alarm"`
}

type Alarm struct {
	Ts           time.Time `json:"time"`
	Topic        string    `json:"topic"`
	Value        string    `json:"value"`
	Desc         string    `json:"desc"`
	AlarmType    string    `json:"alarm_type"`
	AlarmContact string    `json:"alarm_contact"`
	Username     string    `json:"username"`
	Msg          string    `json:"msg"`
}

type AlertTask struct {
	Payload   string
	Rule      ThresholdRule
	Value     float64
	Retries   int  // 重试次数
	IsTrigger bool // 是否触发预警
}
