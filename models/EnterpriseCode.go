/*
 * @Author: ZXH
 * @Date: 2025-01-07 15:54:27
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-01-09 09:24:22
 * @Description:
 */
package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type EnterpriseCode struct {
	ID        primitive.ObjectID `bson:"_id,omitempty"`
	Code      string             `bson:"code"`
	Used      bool               `bson:"used"`
	CreatedAt time.Time          `bson:"createdAt"`
	UsedAt    *time.Time         `bson:"usedAt,omitempty"`
}
