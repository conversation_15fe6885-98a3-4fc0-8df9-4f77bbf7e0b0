package wireguardmgr

import (
	"fmt"
	"log"
	"os/exec"
	"strings"
	"sync"
)

// NamespaceManager handles network namespace operations for WireGuard isolation
type NamespaceManager struct {
	mu sync.Mutex
}

// NewNamespaceManager creates a new namespace manager
func NewNamespaceManager() *NamespaceManager {
	return &NamespaceManager{}
}

// CreateNamespace creates a new network namespace
func (nm *NamespaceManager) CreateNamespace(nsName string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// Check if namespace already exists
	if nm.namespaceExists(nsName) {
		log.Printf("Namespace %s already exists", nsName)
		return nil
	}

	// Create the namespace
	cmd := exec.Command("ip", "netns", "add", nsName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create namespace %s: %v, output: %s", nsName, err, output)
	}

	log.Printf("Successfully created namespace %s", nsName)
	return nil
}

// DeleteNamespace deletes a network namespace
func (nm *NamespaceManager) DeleteNamespace(nsName string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	if !nm.namespaceExists(nsName) {
		log.Printf("Namespace %s does not exist", nsName)
		return nil
	}

	// Delete the namespace
	cmd := exec.Command("ip", "netns", "delete", nsName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete namespace %s: %v, output: %s", nsName, err, output)
	}

	log.Printf("Successfully deleted namespace %s", nsName)
	return nil
}

// namespaceExists checks if a namespace exists
func (nm *NamespaceManager) namespaceExists(nsName string) bool {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false
	}

	namespaces := strings.Split(string(output), "\n")
	for _, ns := range namespaces {
		// Handle both "nsname" and "nsname (id: X)" formats
		nsFields := strings.Fields(ns)
		if len(nsFields) > 0 && nsFields[0] == nsName {
			return true
		}
	}
	return false
}

// ExecInNamespace executes a command within a specific namespace
func (nm *NamespaceManager) ExecInNamespace(nsName string, command string, args ...string) ([]byte, error) {
	// Prepare the command to run in namespace
	cmdArgs := append([]string{"netns", "exec", nsName, command}, args...)
	cmd := exec.Command("ip", cmdArgs...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return output, fmt.Errorf("failed to execute command in namespace %s: %v, output: %s", nsName, err, output)
	}

	return output, nil
}

// SetupNamespaceLoopback sets up the loopback interface in a namespace
func (nm *NamespaceManager) SetupNamespaceLoopback(nsName string) error {
	// Bring up loopback interface
	_, err := nm.ExecInNamespace(nsName, "ip", "link", "set", "lo", "up")
	if err != nil {
		return fmt.Errorf("failed to bring up loopback in namespace %s: %v", nsName, err)
	}

	log.Printf("Successfully set up loopback interface in namespace %s", nsName)
	return nil
}

// CreateVethPair creates a veth pair to connect namespace to host
func (nm *NamespaceManager) CreateVethPair(nsName, vethHost, vethNS string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// Create veth pair
	cmd := exec.Command("ip", "link", "add", vethHost, "type", "veth", "peer", "name", vethNS)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create veth pair: %v, output: %s", err, output)
	}

	// Move one end to namespace
	cmd = exec.Command("ip", "link", "set", vethNS, "netns", nsName)
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to move veth to namespace: %v, output: %s", err, output)
	}

	// Bring up host side
	cmd = exec.Command("ip", "link", "set", vethHost, "up")
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to bring up host veth: %v, output: %s", err, output)
	}

	// Bring up namespace side
	_, err = nm.ExecInNamespace(nsName, "ip", "link", "set", vethNS, "up")
	if err != nil {
		return fmt.Errorf("failed to bring up namespace veth: %v", err)
	}

	log.Printf("Successfully created veth pair %s <-> %s for namespace %s", vethHost, vethNS, nsName)
	return nil
}

// DeleteVethPair deletes a veth pair
func (nm *NamespaceManager) DeleteVethPair(vethHost string) error {
	cmd := exec.Command("ip", "link", "delete", vethHost)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete veth pair: %v, output: %s", err, output)
	}

	log.Printf("Successfully deleted veth pair %s", vethHost)
	return nil
}

// ListNamespaces returns a list of all network namespaces
func (nm *NamespaceManager) ListNamespaces() ([]string, error) {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces: %v", err)
	}

	var namespaces []string
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// Handle both "nsname" and "nsname (id: X)" formats
			fields := strings.Fields(line)
			if len(fields) > 0 {
				namespaces = append(namespaces, fields[0])
			}
		}
	}

	return namespaces, nil
}

// GenerateNamespaceName generates a unique namespace name for a device
func (nm *NamespaceManager) GenerateNamespaceName(deviceName, enterpriseID string) string {
	// Use a combination of enterprise ID and device name to ensure uniqueness
	return fmt.Sprintf("wg-%s-%s", enterpriseID, deviceName)
}

// SetupNamespaceNetworking sets up networking for WireGuard namespace connectivity
func (nm *NamespaceManager) SetupNamespaceNetworking(nsName string, listenPort int, hostInterface string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	vethHost := fmt.Sprintf("veth-h-%s", nsName[3:13]) // Truncate for interface name limits
	vethNS := fmt.Sprintf("veth-n-%s", nsName[3:13])

	// Create veth pair
	if err := nm.CreateVethPair(nsName, vethHost, vethNS); err != nil {
		return fmt.Errorf("failed to create veth pair: %v", err)
	}

	// Assign IP addresses to veth interfaces
	hostIP := "***********/30" // Link-local range for veth communication
	nsIP := "***********/30"

	// Configure host side veth
	cmd := exec.Command("ip", "addr", "add", hostIP, "dev", vethHost)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to configure host veth IP: %v, output: %s", err, output)
	}

	// Configure namespace side veth
	_, err := nm.ExecInNamespace(nsName, "ip", "addr", "add", nsIP, "dev", vethNS)
	if err != nil {
		return fmt.Errorf("failed to configure namespace veth IP: %v", err)
	}

	// Add default route in namespace to host
	_, err = nm.ExecInNamespace(nsName, "ip", "route", "add", "default", "via", "***********")
	if err != nil {
		return fmt.Errorf("failed to add default route in namespace: %v", err)
	}

	// Set up DNAT rule to forward traffic to namespace
	if err := nm.setupDNAT(listenPort, "***********", hostInterface); err != nil {
		return fmt.Errorf("failed to setup DNAT: %v", err)
	}

	// Set up SNAT for return traffic
	if err := nm.setupSNAT("***********", hostInterface); err != nil {
		return fmt.Errorf("failed to setup SNAT: %v", err)
	}

	log.Printf("Successfully set up networking for namespace %s on port %d", nsName, listenPort)
	return nil
}

// setupDNAT sets up DNAT rules to forward traffic to namespace
func (nm *NamespaceManager) setupDNAT(listenPort int, targetIP, hostInterface string) error {
	// DNAT rule to forward UDP traffic to namespace
	cmd := exec.Command("iptables", "-t", "nat", "-A", "PREROUTING",
		"-i", hostInterface, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "DNAT", "--to-destination", fmt.Sprintf("%s:%d", targetIP, listenPort))

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to add DNAT rule: %v, output: %s", err, output)
	}

	// Allow forwarding to namespace
	cmd = exec.Command("iptables", "-A", "FORWARD",
		"-d", targetIP, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "ACCEPT")

	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to add forward rule: %v, output: %s", err, output)
	}

	return nil
}

// setupSNAT sets up SNAT for return traffic from namespace
func (nm *NamespaceManager) setupSNAT(sourceIP, hostInterface string) error {
	// SNAT rule for traffic from namespace
	cmd := exec.Command("iptables", "-t", "nat", "-A", "POSTROUTING",
		"-s", sourceIP, "-o", hostInterface,
		"-j", "MASQUERADE")

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to add SNAT rule: %v, output: %s", err, output)
	}

	return nil
}

// CleanupNamespaceNetworking removes networking configuration for a namespace
func (nm *NamespaceManager) CleanupNamespaceNetworking(nsName string, listenPort int, hostInterface string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	vethHost := fmt.Sprintf("veth-h-%s", nsName[3:13])
	targetIP := "***********"

	// Remove DNAT rule
	cmd := exec.Command("iptables", "-t", "nat", "-D", "PREROUTING",
		"-i", hostInterface, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "DNAT", "--to-destination", fmt.Sprintf("%s:%d", targetIP, listenPort))
	cmd.CombinedOutput() // Ignore errors for cleanup

	// Remove forward rule
	cmd = exec.Command("iptables", "-D", "FORWARD",
		"-d", targetIP, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "ACCEPT")
	cmd.CombinedOutput() // Ignore errors for cleanup

	// Remove SNAT rule
	cmd = exec.Command("iptables", "-t", "nat", "-D", "POSTROUTING",
		"-s", targetIP, "-o", hostInterface,
		"-j", "MASQUERADE")
	cmd.CombinedOutput() // Ignore errors for cleanup

	// Remove veth pair
	if err := nm.DeleteVethPair(vethHost); err != nil {
		log.Printf("Warning: failed to delete veth pair: %v", err)
	}

	log.Printf("Cleaned up networking for namespace %s", nsName)
	return nil
}
