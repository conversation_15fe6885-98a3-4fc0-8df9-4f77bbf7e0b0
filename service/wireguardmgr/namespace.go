package wireguardmgr

import (
	"context"
	"fmt"
	"log"
	"os/exec"
	"strings"
	"sync"
	"time"
)

// NamespaceManager handles network namespace operations for WireGuard isolation
type NamespaceManager struct {
	mu sync.Mutex
}

// execCommandWithTimeout executes a command with a timeout
func (nm *NamespaceManager) execCommandWithTimeout(timeout time.Duration, name string, args ...string) ([]byte, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	cmd := exec.CommandContext(ctx, name, args...)
	output, err := cmd.CombinedOutput()

	if ctx.Err() == context.DeadlineExceeded {
		return output, fmt.Errorf("command timed out after %v: %s %v", timeout, name, args)
	}

	return output, err
}

// NewNamespaceManager creates a new namespace manager
func NewNamespaceManager() *NamespaceManager {
	return &NamespaceManager{}
}

// CreateNamespace creates a new network namespace
func (nm *NamespaceManager) CreateNamespace(nsName string) error {
	log.Printf("Creating namespace: %s", nsName)
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// Check if namespace already exists
	if nm.namespaceExists(nsName) {
		log.Printf("Namespace %s already exists", nsName)
		return nil
	}

	// Create the namespace with timeout
	log.Printf("Executing: ip netns add %s", nsName)
	output, err := nm.execCommandWithTimeout(30*time.Second, "ip", "netns", "add", nsName)
	if err != nil {
		return fmt.Errorf("failed to create namespace %s: %v, output: %s", nsName, err, output)
	}

	log.Printf("Successfully created namespace %s", nsName)
	return nil
}

// DeleteNamespace deletes a network namespace
func (nm *NamespaceManager) DeleteNamespace(nsName string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	if !nm.namespaceExists(nsName) {
		log.Printf("Namespace %s does not exist", nsName)
		return nil
	}

	// Delete the namespace
	cmd := exec.Command("ip", "netns", "delete", nsName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete namespace %s: %v, output: %s", nsName, err, output)
	}

	log.Printf("Successfully deleted namespace %s", nsName)
	return nil
}

// namespaceExists checks if a namespace exists
func (nm *NamespaceManager) namespaceExists(nsName string) bool {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false
	}

	namespaces := strings.Split(string(output), "\n")
	for _, ns := range namespaces {
		// Handle both "nsname" and "nsname (id: X)" formats
		nsFields := strings.Fields(ns)
		if len(nsFields) > 0 && nsFields[0] == nsName {
			return true
		}
	}
	return false
}

// ExecInNamespace executes a command within a specific namespace
func (nm *NamespaceManager) ExecInNamespace(nsName string, command string, args ...string) ([]byte, error) {
	// Prepare the command to run in namespace
	cmdArgs := append([]string{"netns", "exec", nsName, command}, args...)
	log.Printf("Executing in namespace %s: ip %v", nsName, cmdArgs)

	output, err := nm.execCommandWithTimeout(30*time.Second, "ip", cmdArgs...)
	if err != nil {
		return output, fmt.Errorf("failed to execute command in namespace %s: %v, output: %s", nsName, err, output)
	}

	return output, nil
}

// SetupNamespaceLoopback sets up the loopback interface in a namespace
func (nm *NamespaceManager) SetupNamespaceLoopback(nsName string) error {
	log.Printf("Setting up loopback interface in namespace: %s", nsName)
	// Bring up loopback interface
	_, err := nm.ExecInNamespace(nsName, "ip", "link", "set", "lo", "up")
	if err != nil {
		return fmt.Errorf("failed to bring up loopback in namespace %s: %v", nsName, err)
	}

	log.Printf("Successfully set up loopback interface in namespace %s", nsName)
	return nil
}

// CreateVethPair creates a veth pair to connect namespace to host
func (nm *NamespaceManager) CreateVethPair(nsName, vethHost, vethNS string) error {
	// Note: This function is called from SetupNamespaceNetworking which already holds the lock

	log.Printf("Creating veth pair: %s <-> %s", vethHost, vethNS)
	// Create veth pair
	output, err := nm.execCommandWithTimeout(30*time.Second, "ip", "link", "add", vethHost, "type", "veth", "peer", "name", vethNS)
	if err != nil {
		return fmt.Errorf("failed to create veth pair: %v, output: %s", err, output)
	}

	log.Printf("Moving %s to namespace %s", vethNS, nsName)
	// Move one end to namespace
	output, err = nm.execCommandWithTimeout(30*time.Second, "ip", "link", "set", vethNS, "netns", nsName)
	if err != nil {
		return fmt.Errorf("failed to move veth to namespace: %v, output: %s", err, output)
	}

	log.Printf("Bringing up host side veth: %s", vethHost)
	// Bring up host side
	output, err = nm.execCommandWithTimeout(30*time.Second, "ip", "link", "set", vethHost, "up")
	if err != nil {
		return fmt.Errorf("failed to bring up host veth: %v, output: %s", err, output)
	}

	log.Printf("Bringing up namespace side veth: %s", vethNS)
	// Bring up namespace side
	_, err = nm.ExecInNamespace(nsName, "ip", "link", "set", vethNS, "up")
	if err != nil {
		return fmt.Errorf("failed to bring up namespace veth: %v", err)
	}

	log.Printf("Successfully created veth pair %s <-> %s for namespace %s", vethHost, vethNS, nsName)
	return nil
}

// DeleteVethPair deletes a veth pair
func (nm *NamespaceManager) DeleteVethPair(vethHost string) error {
	cmd := exec.Command("ip", "link", "delete", vethHost)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete veth pair: %v, output: %s", err, output)
	}

	log.Printf("Successfully deleted veth pair %s", vethHost)
	return nil
}

// ListNamespaces returns a list of all network namespaces
func (nm *NamespaceManager) ListNamespaces() ([]string, error) {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces: %v", err)
	}

	var namespaces []string
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// Handle both "nsname" and "nsname (id: X)" formats
			fields := strings.Fields(line)
			if len(fields) > 0 {
				namespaces = append(namespaces, fields[0])
			}
		}
	}

	return namespaces, nil
}

// GenerateNamespaceName generates a unique namespace name for a device
func (nm *NamespaceManager) GenerateNamespaceName(deviceName, enterpriseID string) string {
	// Use a combination of enterprise ID and device name to ensure uniqueness
	return fmt.Sprintf("wg-%s-%s", enterpriseID, deviceName)
}

// SetupNamespaceNetworking sets up networking for WireGuard namespace connectivity
func (nm *NamespaceManager) SetupNamespaceNetworking(nsName string, listenPort int, hostInterface string) error {
	log.Printf("Setting up networking for namespace %s on port %d with interface %s", nsName, listenPort, hostInterface)
	nm.mu.Lock()
	defer nm.mu.Unlock()

	vethHost := fmt.Sprintf("veth-h-%s", nsName[3:13]) // Truncate for interface name limits
	vethNS := fmt.Sprintf("veth-n-%s", nsName[3:13])

	log.Printf("Creating veth pair: %s <-> %s", vethHost, vethNS)
	// Create veth pair
	if err := nm.CreateVethPair(nsName, vethHost, vethNS); err != nil {
		return fmt.Errorf("failed to create veth pair: %v", err)
	}
	log.Printf("Successfully created veth pair")

	// Assign IP addresses to veth interfaces
	hostIP := "***********/30" // Link-local range for veth communication
	nsIP := "***********/30"

	// Configure host side veth
	cmd := exec.Command("ip", "addr", "add", hostIP, "dev", vethHost)
	if output, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to configure host veth IP: %v, output: %s", err, output)
	}

	// Configure namespace side veth
	_, err := nm.ExecInNamespace(nsName, "ip", "addr", "add", nsIP, "dev", vethNS)
	if err != nil {
		return fmt.Errorf("failed to configure namespace veth IP: %v", err)
	}

	// Add default route in namespace to host
	_, err = nm.ExecInNamespace(nsName, "ip", "route", "add", "default", "via", "***********")
	if err != nil {
		return fmt.Errorf("failed to add default route in namespace: %v", err)
	}

	// Set up DNAT rule to forward traffic to namespace
	if err := nm.setupDNAT(listenPort, "***********", hostInterface); err != nil {
		return fmt.Errorf("failed to setup DNAT: %v", err)
	}

	// Set up SNAT for return traffic
	if err := nm.setupSNAT("***********", hostInterface); err != nil {
		return fmt.Errorf("failed to setup SNAT: %v", err)
	}

	log.Printf("Successfully set up networking for namespace %s on port %d", nsName, listenPort)
	return nil
}

// setupDNAT sets up DNAT rules to forward traffic to namespace
func (nm *NamespaceManager) setupDNAT(listenPort int, targetIP, hostInterface string) error {
	log.Printf("Setting up DNAT rule for port %d to %s via %s", listenPort, targetIP, hostInterface)

	// DNAT rule to forward UDP traffic to namespace
	output, err := nm.execCommandWithTimeout(30*time.Second, "iptables", "-t", "nat", "-A", "PREROUTING",
		"-i", hostInterface, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "DNAT", "--to-destination", fmt.Sprintf("%s:%d", targetIP, listenPort))
	if err != nil {
		return fmt.Errorf("failed to add DNAT rule: %v, output: %s", err, output)
	}

	log.Printf("Setting up FORWARD rule for port %d to %s", listenPort, targetIP)
	// Allow forwarding to namespace
	output, err = nm.execCommandWithTimeout(30*time.Second, "iptables", "-A", "FORWARD",
		"-d", targetIP, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "ACCEPT")
	if err != nil {
		return fmt.Errorf("failed to add forward rule: %v, output: %s", err, output)
	}

	log.Printf("Successfully set up DNAT and FORWARD rules")
	return nil
}

// setupSNAT sets up SNAT for return traffic from namespace
func (nm *NamespaceManager) setupSNAT(sourceIP, hostInterface string) error {
	log.Printf("Setting up SNAT rule for %s via %s", sourceIP, hostInterface)

	// SNAT rule for traffic from namespace
	output, err := nm.execCommandWithTimeout(30*time.Second, "iptables", "-t", "nat", "-A", "POSTROUTING",
		"-s", sourceIP, "-o", hostInterface,
		"-j", "MASQUERADE")
	if err != nil {
		return fmt.Errorf("failed to add SNAT rule: %v, output: %s", err, output)
	}

	log.Printf("Successfully set up SNAT rule")
	return nil
}

// CleanupNamespaceNetworking removes networking configuration for a namespace
func (nm *NamespaceManager) CleanupNamespaceNetworking(nsName string, listenPort int, hostInterface string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	vethHost := fmt.Sprintf("veth-h-%s", nsName[3:13])
	targetIP := "***********"

	// Remove DNAT rule
	cmd := exec.Command("iptables", "-t", "nat", "-D", "PREROUTING",
		"-i", hostInterface, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "DNAT", "--to-destination", fmt.Sprintf("%s:%d", targetIP, listenPort))
	cmd.CombinedOutput() // Ignore errors for cleanup

	// Remove forward rule
	cmd = exec.Command("iptables", "-D", "FORWARD",
		"-d", targetIP, "-p", "udp", "--dport", fmt.Sprintf("%d", listenPort),
		"-j", "ACCEPT")
	cmd.CombinedOutput() // Ignore errors for cleanup

	// Remove SNAT rule
	cmd = exec.Command("iptables", "-t", "nat", "-D", "POSTROUTING",
		"-s", targetIP, "-o", hostInterface,
		"-j", "MASQUERADE")
	cmd.CombinedOutput() // Ignore errors for cleanup

	// Remove veth pair
	if err := nm.DeleteVethPair(vethHost); err != nil {
		log.Printf("Warning: failed to delete veth pair: %v", err)
	}

	log.Printf("Cleaned up networking for namespace %s", nsName)
	return nil
}
