package wireguardmgr

import (
	"fmt"
	"log"
	"os/exec"
	"strings"
	"sync"
)

// NamespaceManager handles network namespace operations for WireGuard isolation
type NamespaceManager struct {
	mu sync.Mutex
}

// NewNamespaceManager creates a new namespace manager
func NewNamespaceManager() *NamespaceManager {
	return &NamespaceManager{}
}

// CreateNamespace creates a new network namespace
func (nm *NamespaceManager) CreateNamespace(nsName string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// Check if namespace already exists
	if nm.namespaceExists(nsName) {
		log.Printf("Namespace %s already exists", nsName)
		return nil
	}

	// Create the namespace
	cmd := exec.Command("ip", "netns", "add", nsName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create namespace %s: %v, output: %s", nsName, err, output)
	}

	log.Printf("Successfully created namespace %s", nsName)
	return nil
}

// DeleteNamespace deletes a network namespace
func (nm *NamespaceManager) DeleteNamespace(nsName string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	if !nm.namespaceExists(nsName) {
		log.Printf("Namespace %s does not exist", nsName)
		return nil
	}

	// Delete the namespace
	cmd := exec.Command("ip", "netns", "delete", nsName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete namespace %s: %v, output: %s", nsName, err, output)
	}

	log.Printf("Successfully deleted namespace %s", nsName)
	return nil
}

// namespaceExists checks if a namespace exists
func (nm *NamespaceManager) namespaceExists(nsName string) bool {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false
	}

	namespaces := strings.Split(string(output), "\n")
	for _, ns := range namespaces {
		// Handle both "nsname" and "nsname (id: X)" formats
		nsFields := strings.Fields(ns)
		if len(nsFields) > 0 && nsFields[0] == nsName {
			return true
		}
	}
	return false
}

// ExecInNamespace executes a command within a specific namespace
func (nm *NamespaceManager) ExecInNamespace(nsName string, command string, args ...string) ([]byte, error) {
	// Prepare the command to run in namespace
	cmdArgs := append([]string{"netns", "exec", nsName, command}, args...)
	cmd := exec.Command("ip", cmdArgs...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return output, fmt.Errorf("failed to execute command in namespace %s: %v, output: %s", nsName, err, output)
	}

	return output, nil
}

// SetupNamespaceLoopback sets up the loopback interface in a namespace
func (nm *NamespaceManager) SetupNamespaceLoopback(nsName string) error {
	// Bring up loopback interface
	_, err := nm.ExecInNamespace(nsName, "ip", "link", "set", "lo", "up")
	if err != nil {
		return fmt.Errorf("failed to bring up loopback in namespace %s: %v", nsName, err)
	}

	log.Printf("Successfully set up loopback interface in namespace %s", nsName)
	return nil
}

// CreateVethPair creates a veth pair to connect namespace to host
func (nm *NamespaceManager) CreateVethPair(nsName, vethHost, vethNS string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// Create veth pair
	cmd := exec.Command("ip", "link", "add", vethHost, "type", "veth", "peer", "name", vethNS)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create veth pair: %v, output: %s", err, output)
	}

	// Move one end to namespace
	cmd = exec.Command("ip", "link", "set", vethNS, "netns", nsName)
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to move veth to namespace: %v, output: %s", err, output)
	}

	// Bring up host side
	cmd = exec.Command("ip", "link", "set", vethHost, "up")
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to bring up host veth: %v, output: %s", err, output)
	}

	// Bring up namespace side
	_, err = nm.ExecInNamespace(nsName, "ip", "link", "set", vethNS, "up")
	if err != nil {
		return fmt.Errorf("failed to bring up namespace veth: %v", err)
	}

	log.Printf("Successfully created veth pair %s <-> %s for namespace %s", vethHost, vethNS, nsName)
	return nil
}

// DeleteVethPair deletes a veth pair
func (nm *NamespaceManager) DeleteVethPair(vethHost string) error {
	cmd := exec.Command("ip", "link", "delete", vethHost)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete veth pair: %v, output: %s", err, output)
	}

	log.Printf("Successfully deleted veth pair %s", vethHost)
	return nil
}

// ListNamespaces returns a list of all network namespaces
func (nm *NamespaceManager) ListNamespaces() ([]string, error) {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces: %v", err)
	}

	var namespaces []string
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// Handle both "nsname" and "nsname (id: X)" formats
			fields := strings.Fields(line)
			if len(fields) > 0 {
				namespaces = append(namespaces, fields[0])
			}
		}
	}

	return namespaces, nil
}

// GenerateNamespaceName generates a unique namespace name for a device
func (nm *NamespaceManager) GenerateNamespaceName(deviceName, enterpriseID string) string {
	// Use a combination of enterprise ID and device name to ensure uniqueness
	return fmt.Sprintf("wg-%s-%s", enterpriseID, deviceName)
}

// generateShortVethName generates a short veth interface name (max 15 chars)
func (nm *NamespaceManager) generateShortVethName(nsName, suffix string) string {
	// Linux interface names are limited to 15 characters
	// Create a hash of the namespace name to ensure uniqueness while keeping it short
	// Simple hash function to avoid import issues for now
	hashSum := 0
	for _, c := range nsName {
		hashSum = hashSum*31 + int(c)
	}
	hash := fmt.Sprintf("%08x", hashSum)[:8]  // Use first 8 chars of hash
	return fmt.Sprintf("v%s%s", hash, suffix) // v + 8 chars hash + suffix = max 15 chars
}

// SetupDNATForNamespace sets up DNAT rules to allow external access to namespace-isolated WireGuard interfaces
func (nm *NamespaceManager) SetupDNATForNamespace(nsName string, wgPort int, hostInterface string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// Create veth pair to connect namespace to host
	// Use short names to avoid Linux 15-character interface name limit
	vethHost := nm.generateShortVethName(nsName, "h") // v + 8 chars hash + h = 10 chars
	vethNS := nm.generateShortVethName(nsName, "n")   // v + 8 chars hash + n = 10 chars

	// Create veth pair
	cmd := exec.Command("ip", "link", "add", vethHost, "type", "veth", "peer", "name", vethNS)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create veth pair: %v, output: %s", err, output)
	}

	// Move one end to namespace
	cmd = exec.Command("ip", "link", "set", vethNS, "netns", nsName)
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to move veth to namespace: %v, output: %s", err, output)
	}

	// Configure host side veth
	cmd = exec.Command("ip", "addr", "add", "*************/30", "dev", vethHost)
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to configure host veth: %v, output: %s", err, output)
	}

	cmd = exec.Command("ip", "link", "set", vethHost, "up")
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to bring up host veth: %v, output: %s", err, output)
	}

	// Configure namespace side veth
	_, err = nm.ExecInNamespace(nsName, "ip", "addr", "add", "*************/30", "dev", vethNS)
	if err != nil {
		return fmt.Errorf("failed to configure namespace veth: %v", err)
	}

	_, err = nm.ExecInNamespace(nsName, "ip", "link", "set", vethNS, "up")
	if err != nil {
		return fmt.Errorf("failed to bring up namespace veth: %v", err)
	}

	// Add default route in namespace
	_, err = nm.ExecInNamespace(nsName, "ip", "route", "add", "default", "via", "*************")
	if err != nil {
		return fmt.Errorf("failed to add default route in namespace: %v", err)
	}

	// Set up DNAT rule on host to forward traffic to namespace
	cmd = exec.Command("iptables", "-t", "nat", "-A", "PREROUTING", "-i", hostInterface, "-p", "udp", "--dport", fmt.Sprintf("%d", wgPort), "-j", "DNAT", "--to-destination", fmt.Sprintf("*************:%d", wgPort))
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to add DNAT rule: %v, output: %s", err, output)
	}

	// Set up SNAT rule for return traffic
	cmd = exec.Command("iptables", "-t", "nat", "-A", "POSTROUTING", "-s", "*************/30", "-j", "MASQUERADE")
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to add SNAT rule: %v, output: %s", err, output)
	}

	// Enable IP forwarding
	cmd = exec.Command("sysctl", "-w", "net.ipv4.ip_forward=1")
	output, err = cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to enable IP forwarding: %v, output: %s", err, output)
	}

	log.Printf("Successfully set up DNAT for namespace %s on port %d", nsName, wgPort)
	return nil
}

// CleanupDNATForNamespace removes DNAT rules and veth interfaces for a namespace
func (nm *NamespaceManager) CleanupDNATForNamespace(nsName string, wgPort int, hostInterface string) error {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	vethHost := nm.generateShortVethName(nsName, "h")

	// Remove DNAT rule
	cmd := exec.Command("iptables", "-t", "nat", "-D", "PREROUTING", "-i", hostInterface, "-p", "udp", "--dport", fmt.Sprintf("%d", wgPort), "-j", "DNAT", "--to-destination", fmt.Sprintf("*************:%d", wgPort))
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Warning: failed to remove DNAT rule: %v, output: %s", err, output)
	}

	// Remove SNAT rule (this might affect other namespaces, so be careful)
	// We'll leave the MASQUERADE rule as it might be used by other namespaces

	// Remove veth interface (this will also remove the peer in the namespace)
	cmd = exec.Command("ip", "link", "delete", vethHost)
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("Warning: failed to remove veth interface: %v, output: %s", err, output)
	}

	log.Printf("Cleaned up DNAT for namespace %s", nsName)
	return nil
}

// SetupNamespaceConnectivity sets up both loopback and external connectivity for a namespace
func (nm *NamespaceManager) SetupNamespaceConnectivity(nsName string, wgPort int, hostInterface string) error {
	// Set up loopback
	if err := nm.SetupNamespaceLoopback(nsName); err != nil {
		return fmt.Errorf("failed to setup loopback: %v", err)
	}

	// Set up DNAT for external connectivity
	if err := nm.SetupDNATForNamespace(nsName, wgPort, hostInterface); err != nil {
		return fmt.Errorf("failed to setup DNAT: %v", err)
	}

	return nil
}
