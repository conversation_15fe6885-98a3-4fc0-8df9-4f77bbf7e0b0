package wireguardmgr

import (
	"beacon/cloud/db"
	"context"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type IPAMRecord struct {
	EnterpriseID string   `bson:"enterprise_id"`
	Network      string   `bson:"network"` // CIDR format
	UsedIPs      []string `bson:"used_ips"`
	NextIP       string   `bson:"next_ip"`
}

// IPAM manages IP address allocation for WireGuard networks
type IPAM struct {
	mu            sync.Mutex
	baseNetwork   *net.IPNet
	enterpriseMap map[string]*EnterpriseIPAM
}

// EnterpriseIPAM manages IP allocation for a single enterprise
type EnterpriseIPAM struct {
	EnterpriseID string
	Network      *net.IPNet
	UsedIPs      map[string]bool
	NextIP       net.IP
}

func NewIPAM() *IPAM {
	ipam := &IPAM{
		baseNetwork:   mustParseCIDR("10.0.0.0/16"),
		enterpriseMap: make(map[string]*EnterpriseIPAM),
	}

	// Restore enterprise subnet information from database
	devices, err := db.MongoDB.Collection("wg_devices").Find(context.Background(), bson.M{})
	if err != nil {
		log.Printf("Failed to load devices for IPAM initialization: %v", err)
		return ipam
	}
	defer devices.Close(context.Background())

	for devices.Next(context.Background()) {
		var device Device
		if err := devices.Decode(&device); err != nil {
			continue
		}

		// Parse subnet from device IP
		_, network, err := net.ParseCIDR(device.IP)
		if err != nil {
			continue
		}

		// Initialize enterprise IPAM
		ipam.enterpriseMap[device.EnterpriseID] = &EnterpriseIPAM{
			EnterpriseID: device.EnterpriseID,
			Network:      network,
			UsedIPs:      make(map[string]bool),
			NextIP:       network.IP,
		}
	}

	return ipam
}

func mustParseCIDR(s string) *net.IPNet {
	_, n, _ := net.ParseCIDR(s)
	return n
}

func (i *IPAM) AllocateEnterpriseSubnet(enterpriseID string) (*EnterpriseIPAM, error) {
	i.mu.Lock()
	defer i.mu.Unlock()

	log.Printf("Checking existing IPAM record for enterprise: %s", enterpriseID)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if allocation record already exists
	var record IPAMRecord
	err := db.MongoDB.Collection("ipam_records").FindOne(
		ctx,
		bson.M{"enterprise_id": enterpriseID},
	).Decode(&record)

	if err == nil {
		log.Printf("Found existing IPAM record with network: %s", record.Network)
		return createEnterpriseIPAM(&record)
	}

	log.Printf("Looking for last allocated subnet")
	opts := options.FindOne().SetSort(bson.D{{Key: "network", Value: -1}})
	var lastRecord IPAMRecord
	err = db.MongoDB.Collection("ipam_records").FindOne(ctx, bson.M{}, opts).Decode(&lastRecord)

	var subnetNum int
	if err == mongo.ErrNoDocuments {
		log.Printf("No existing subnets found, starting with subnet 1")
		subnetNum = 1
	} else if err != nil {
		return nil, fmt.Errorf("failed to query last subnet: %v", err)
	} else {
		_, lastNet, _ := net.ParseCIDR(lastRecord.Network)
		subnetNum = int(lastNet.IP[2]) + 1
		log.Printf("Found last subnet: %s, next subnet number will be: %d", lastRecord.Network, subnetNum)
	}

	if subnetNum > 255 {
		return nil, fmt.Errorf("no more subnets available")
	}

	// 创建新的子网记录
	subnet := fmt.Sprintf("10.0.%d.0/24", subnetNum)
	_, network, _ := net.ParseCIDR(subnet)
	nextIP := make(net.IP, len(network.IP))
	copy(nextIP, network.IP)
	nextIP[3] = 1

	newRecord := IPAMRecord{
		EnterpriseID: enterpriseID,
		Network:      subnet,
		UsedIPs:      []string{},
		NextIP:       nextIP.String(),
	}

	_, err = db.MongoDB.Collection("ipam_records").InsertOne(ctx, newRecord)
	if err != nil {
		return nil, fmt.Errorf("failed to save IPAM record: %v", err)
	}

	log.Printf("Created new IPAM record with network: %s", subnet)
	return createEnterpriseIPAM(&newRecord)
}

// Helper function to create EnterpriseIPAM from IPAMRecord
func createEnterpriseIPAM(record *IPAMRecord) (*EnterpriseIPAM, error) {
	_, network, _ := net.ParseCIDR(record.Network)
	nextIP := net.ParseIP(record.NextIP)
	usedIPs := make(map[string]bool)
	for _, ip := range record.UsedIPs {
		usedIPs[ip] = true
	}
	return &EnterpriseIPAM{
		EnterpriseID: record.EnterpriseID,
		Network:      network,
		UsedIPs:      usedIPs,
		NextIP:       nextIP,
	}, nil
}

func (e *EnterpriseIPAM) AllocateIP() (string, error) {
	// Get correct subnet number from network information
	subnetNum := e.Network.IP[2] // Use Network.IP instead of NextIP

	log.Printf("Allocating IP from subnet 10.0.%d.0/24", subnetNum)
	for i := 1; i < 254; i++ {
		ip := fmt.Sprintf("10.0.%d.%d", subnetNum, i)
		if !e.UsedIPs[ip] {
			e.UsedIPs[ip] = true
			// 更新 MongoDB 中的记录
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			update := bson.M{
				"$push": bson.M{"used_ips": ip},
			}
			_, err := db.MongoDB.Collection("ipam_records").UpdateOne(
				ctx,
				bson.M{"enterprise_id": e.EnterpriseID},
				update,
			)
			if err != nil {
				delete(e.UsedIPs, ip)
				return "", fmt.Errorf("failed to update IPAM record: %v", err)
			}
			log.Printf("Allocated IP %s from subnet 10.0.%d.0/24", ip, subnetNum)
			return ip, nil
		}
	}
	return "", fmt.Errorf("no available IPs in subnet 10.0.%d.0/24", subnetNum)
}

func (e *EnterpriseIPAM) ReleaseIP(ip string) {
	delete(e.UsedIPs, ip)
	// 更新 MongoDB 中的记录
	update := bson.M{
		"$pull": bson.M{"used_ips": ip},
	}
	db.MongoDB.Collection("ipam_records").UpdateOne(
		context.Background(),
		bson.M{"enterprise_id": e.EnterpriseID},
		update,
	)
}

// GetEnterpriseIPAM returns the IPAM for a specific enterprise
func (i *IPAM) GetEnterpriseIPAM(enterpriseID string) (*EnterpriseIPAM, error) {
	i.mu.Lock()
	defer i.mu.Unlock()

	log.Printf("GetEnterpriseIPAM: Starting query for enterprise: %s", enterpriseID)

	// 创建带超时的 context
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 从 MongoDB 查询企业网段信息
	var record IPAMRecord
	err := db.MongoDB.Collection("ipam_records").FindOne(
		ctx,
		bson.M{"enterprise_id": enterpriseID},
	).Decode(&record)

	if err == mongo.ErrNoDocuments {
		log.Printf("GetEnterpriseIPAM: No record found for enterprise: %s", enterpriseID)
		return nil, fmt.Errorf("no IPAM found for enterprise %s", enterpriseID)
	}
	if err != nil {
		log.Printf("GetEnterpriseIPAM: Error querying record: %v", err)
		return nil, fmt.Errorf("failed to query IPAM record: %v", err)
	}

	log.Printf("GetEnterpriseIPAM: Found record with network: %s", record.Network)

	_, network, _ := net.ParseCIDR(record.Network)
	nextIP := net.ParseIP(record.NextIP)
	usedIPs := make(map[string]bool)
	for _, ip := range record.UsedIPs {
		usedIPs[ip] = true
	}

	log.Printf("GetEnterpriseIPAM: Successfully created IPAM for enterprise: %s", enterpriseID)

	return &EnterpriseIPAM{
		EnterpriseID: enterpriseID,
		Network:      network,
		UsedIPs:      usedIPs,
		NextIP:       nextIP,
	}, nil
}
