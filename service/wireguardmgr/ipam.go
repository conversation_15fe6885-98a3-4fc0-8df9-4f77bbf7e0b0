package wireguardmgr

import (
	"beacon/cloud/db"
	"context"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type IPAMRecord struct {
	EnterpriseID string   `bson:"enterprise_id"`
	DeviceName   string   `bson:"device_name"` // WireGuard device name
	Network      string   `bson:"network"`     // CIDR format for this device
	UsedIPs      []string `bson:"used_ips"`    // IPs allocated to peers
	NextIP       string   `bson:"next_ip"`     // Next available IP for peers
}

// IPA<PERSON> manages IP address allocation for WireGuard networks
type IPAM struct {
	mu          sync.Mutex
	baseNetwork *net.IPNet
	deviceMap   map[string]*DeviceIPAM // key: enterpriseID-deviceName
}

// DeviceIPAM manages IP allocation for a single WireGuard device
type DeviceIPAM struct {
	EnterpriseID string
	DeviceName   string
	Network      *net.IPNet      // The subnet assigned to this device
	UsedIPs      map[string]bool // IPs allocated to peers
	NextIP       net.IP          // Next available IP for peers (starts from .2)
}

func NewIPAM() *IPAM {
	ipam := &IPAM{
		baseNetwork: mustParseCIDR("10.0.0.0/16"),
		deviceMap:   make(map[string]*DeviceIPAM),
	}

	// Restore device subnet information from database
	devices, err := db.MongoDB.Collection("wg_devices").Find(context.Background(), bson.M{})
	if err != nil {
		log.Printf("Failed to load devices for IPAM initialization: %v", err)
		return ipam
	}
	defer devices.Close(context.Background())

	for devices.Next(context.Background()) {
		var device Device
		if err := devices.Decode(&device); err != nil {
			continue
		}

		// Parse subnet from device IP
		_, network, err := net.ParseCIDR(device.IP)
		if err != nil {
			continue
		}

		// Initialize device IPAM
		deviceKey := fmt.Sprintf("%s-%s", device.EnterpriseID, device.Name)
		nextIP := make(net.IP, len(network.IP))
		copy(nextIP, network.IP)
		nextIP[3] = 2 // Start peer allocation from .2

		ipam.deviceMap[deviceKey] = &DeviceIPAM{
			EnterpriseID: device.EnterpriseID,
			DeviceName:   device.Name,
			Network:      network,
			UsedIPs:      make(map[string]bool),
			NextIP:       nextIP,
		}
	}

	return ipam
}

func mustParseCIDR(s string) *net.IPNet {
	_, n, _ := net.ParseCIDR(s)
	return n
}

// AllocateDeviceSubnet allocates a subnet for a specific WireGuard device
func (i *IPAM) AllocateDeviceSubnet(enterpriseID, deviceName string) (*DeviceIPAM, error) {
	i.mu.Lock()
	defer i.mu.Unlock()

	deviceKey := fmt.Sprintf("%s-%s", enterpriseID, deviceName)
	log.Printf("Allocating subnet for device: %s (key: %s)", deviceName, deviceKey)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if allocation record already exists for this device
	var record IPAMRecord
	err := db.MongoDB.Collection("ipam_records").FindOne(
		ctx,
		bson.M{"enterprise_id": enterpriseID, "device_name": deviceName},
	).Decode(&record)

	if err == nil {
		log.Printf("Found existing IPAM record for device %s with network: %s", deviceName, record.Network)
		return createDeviceIPAM(&record)
	}

	// Find the next available subnet
	log.Printf("Looking for last allocated subnet")
	opts := options.FindOne().SetSort(bson.D{{Key: "network", Value: -1}})
	var lastRecord IPAMRecord
	err = db.MongoDB.Collection("ipam_records").FindOne(ctx, bson.M{}, opts).Decode(&lastRecord)

	var subnetNum int
	if err == mongo.ErrNoDocuments {
		log.Printf("No existing subnets found, starting with subnet 1")
		subnetNum = 1
	} else if err != nil {
		return nil, fmt.Errorf("failed to query last subnet: %v", err)
	} else {
		_, lastNet, _ := net.ParseCIDR(lastRecord.Network)
		subnetNum = int(lastNet.IP[2]) + 1
		log.Printf("Found last subnet: %s, next subnet number will be: %d", lastRecord.Network, subnetNum)
	}

	if subnetNum > 255 {
		return nil, fmt.Errorf("no more subnets available")
	}

	// Create new subnet record for this device
	subnet := fmt.Sprintf("10.0.%d.0/24", subnetNum)
	_, network, _ := net.ParseCIDR(subnet)

	// Device gets .1, peers start from .2
	nextIP := make(net.IP, len(network.IP))
	copy(nextIP, network.IP)
	nextIP[3] = 2

	newRecord := IPAMRecord{
		EnterpriseID: enterpriseID,
		DeviceName:   deviceName,
		Network:      subnet,
		UsedIPs:      []string{}, // Only track peer IPs, device always gets .1
		NextIP:       nextIP.String(),
	}

	_, err = db.MongoDB.Collection("ipam_records").InsertOne(ctx, newRecord)
	if err != nil {
		return nil, fmt.Errorf("failed to save IPAM record: %v", err)
	}

	log.Printf("Created new IPAM record for device %s with network: %s", deviceName, subnet)
	return createDeviceIPAM(&newRecord)
}

// Helper function to create DeviceIPAM from IPAMRecord
func createDeviceIPAM(record *IPAMRecord) (*DeviceIPAM, error) {
	_, network, _ := net.ParseCIDR(record.Network)
	nextIP := net.ParseIP(record.NextIP)
	usedIPs := make(map[string]bool)
	for _, ip := range record.UsedIPs {
		usedIPs[ip] = true
	}
	return &DeviceIPAM{
		EnterpriseID: record.EnterpriseID,
		DeviceName:   record.DeviceName,
		Network:      network,
		UsedIPs:      usedIPs,
		NextIP:       nextIP,
	}, nil
}

// GetDeviceServerIP returns the server IP for a device (always .1 in the subnet)
func (d *DeviceIPAM) GetDeviceServerIP() string {
	serverIP := make(net.IP, len(d.Network.IP))
	copy(serverIP, d.Network.IP)
	serverIP[3] = 1
	return fmt.Sprintf("%s/24", serverIP.String())
}

// AllocateIP allocates an IP for a peer connecting to this device
func (d *DeviceIPAM) AllocateIP() (string, error) {
	// Get correct subnet number from network information
	subnetNum := d.Network.IP[2]

	log.Printf("Allocating IP from subnet 10.0.%d.0/24 for device %s", subnetNum, d.DeviceName)

	// Start from .2 (skip .1 which is reserved for the server)
	for i := 2; i <= 254; i++ {
		ip := fmt.Sprintf("10.0.%d.%d", subnetNum, i)
		if !d.UsedIPs[ip] {
			d.UsedIPs[ip] = true
			// Update MongoDB record
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			update := bson.M{
				"$push": bson.M{"used_ips": ip},
			}
			_, err := db.MongoDB.Collection("ipam_records").UpdateOne(
				ctx,
				bson.M{"enterprise_id": d.EnterpriseID, "device_name": d.DeviceName},
				update,
			)
			if err != nil {
				delete(d.UsedIPs, ip)
				return "", fmt.Errorf("failed to update IPAM record: %v", err)
			}
			log.Printf("Allocated IP %s from subnet 10.0.%d.0/24 for device %s", ip, subnetNum, d.DeviceName)
			return ip, nil
		}
	}
	return "", fmt.Errorf("no available IPs in subnet 10.0.%d.0/24 for device %s", subnetNum, d.DeviceName)
}

// ReleaseIP releases an IP allocated to a peer
func (d *DeviceIPAM) ReleaseIP(ip string) {
	delete(d.UsedIPs, ip)
	// Update MongoDB record
	update := bson.M{
		"$pull": bson.M{"used_ips": ip},
	}
	db.MongoDB.Collection("ipam_records").UpdateOne(
		context.Background(),
		bson.M{"enterprise_id": d.EnterpriseID, "device_name": d.DeviceName},
		update,
	)
}

// GetDeviceIPAM returns the IPAM for a specific device
func (i *IPAM) GetDeviceIPAM(enterpriseID, deviceName string) (*DeviceIPAM, error) {
	i.mu.Lock()
	defer i.mu.Unlock()

	deviceKey := fmt.Sprintf("%s-%s", enterpriseID, deviceName)
	log.Printf("GetDeviceIPAM: Starting query for device: %s (key: %s)", deviceName, deviceKey)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Query device subnet information from MongoDB
	var record IPAMRecord
	err := db.MongoDB.Collection("ipam_records").FindOne(
		ctx,
		bson.M{"enterprise_id": enterpriseID, "device_name": deviceName},
	).Decode(&record)

	if err == mongo.ErrNoDocuments {
		log.Printf("GetDeviceIPAM: No record found for device: %s", deviceName)
		return nil, fmt.Errorf("no IPAM found for device %s", deviceName)
	}
	if err != nil {
		log.Printf("GetDeviceIPAM: Error querying record: %v", err)
		return nil, fmt.Errorf("failed to query IPAM record: %v", err)
	}

	log.Printf("GetDeviceIPAM: Found record with network: %s", record.Network)

	_, network, _ := net.ParseCIDR(record.Network)
	nextIP := net.ParseIP(record.NextIP)
	usedIPs := make(map[string]bool)
	for _, ip := range record.UsedIPs {
		usedIPs[ip] = true
	}

	log.Printf("GetDeviceIPAM: Successfully created IPAM for device: %s", deviceName)

	return &DeviceIPAM{
		EnterpriseID: enterpriseID,
		DeviceName:   deviceName,
		Network:      network,
		UsedIPs:      usedIPs,
		NextIP:       nextIP,
	}, nil
}
