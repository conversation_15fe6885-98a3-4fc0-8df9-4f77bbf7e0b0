package wireguardmgr

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/skip2/go-qrcode"
	"go.mongodb.org/mongo-driver/bson"
)

// WGHelper provides utility functions for WireGuard management using command line tools
type WGHelper struct {
	configPath string
	// Mutex for file operations to prevent concurrent writes
	mu sync.Mutex
}

// NewWGHelper creates a new WireGuard helper
func NewWGHelper(configPath string) *WGHelper {
	if configPath == "" {
		configPath = wgConfigPath
	}
	return &WGHelper{
		configPath: configPath,
	}
}

// generateServerConfig generates server-side WireGuard configuration file
func (m *Manager) generateServerConfig(device *Device) error {
	configPath := fmt.Sprintf("%s/%s.conf", wgConfigPath, device.Name)

	// 获取该设备的所有 peers
	peers, err := m.ListPeers(context.Background(), device.EnterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get peers: %v", err)
	}

	// 生成基础配置
	config := fmt.Sprintf(`[Interface]
PrivateKey = %s
Address = %s
ListenPort = %d
MTU=1420
PreUp = 
PostUp = iptables -t nat -A POSTROUTING -s %s -o %s -j MASQUERADE; iptables -A INPUT -p udp -m udp --dport %d -j ACCEPT; iptables -A FORWARD -i %s -j ACCEPT; iptables -A FORWARD -o %s -j ACCEPT;
PreDown = 
PostDown = iptables -t nat -D POSTROUTING -s %s -o %s -j MASQUERADE; iptables -D INPUT -p udp -m udp --dport %d -j ACCEPT; iptables -D FORWARD -i %s -j ACCEPT; iptables -D FORWARD -o %s -j ACCEPT;
`,
		device.PrivateKey,
		device.IP,
		device.ListenPort,
		strings.TrimSuffix(device.IP, "1/24")+"0/24", // 子网
		config.AppConfig.NetInterface,
		device.ListenPort,
		device.Name,
		device.Name,
		strings.TrimSuffix(device.IP, "1/24")+"0/24", // 子网
		config.AppConfig.NetInterface,
		device.ListenPort,
		device.Name,
		device.Name,
	)

	// 添加所有 peers 配置
	for _, peer := range peers {
		// 获取该peer的额外路由信息
		var additionalRoutes []string
		cursor, err := db.MongoDB.Collection(PeerRoutesCollection).Find(context.Background(),
			bson.M{"peer_id": peer.ID, "enterprise_id": device.EnterpriseID})

		if err == nil {
			defer cursor.Close(context.Background())

			var route struct {
				Network string `bson:"network"`
			}

			for cursor.Next(context.Background()) {
				if err := cursor.Decode(&route); err == nil {
					additionalRoutes = append(additionalRoutes, route.Network)
				}
			}
		}

		// 构建AllowedIPs字段
		allowedIPs := []string{fmt.Sprintf("%s/32", peer.IP)}
		allowedIPs = append(allowedIPs, additionalRoutes...)

		peerConfig := fmt.Sprintf(`
# Client: %s (%s)
[Peer]
PublicKey = %s
PresharedKey = %s
AllowedIPs = %s
`,
			peer.Name,
			peer.ID.String(),
			peer.PublicKey,
			peer.PresharedKey,
			strings.Join(allowedIPs, ", "),
		)
		config += peerConfig
	}

	return m.Helper.WriteFile(configPath, []byte(config), 0600)
}

// GeneratePeerConfig 生成对等点配置
func (m *Manager) GeneratePeerConfig(peer *Peer) (string, error) {
	// 获取设备信息
	var device Device
	err := m.DevicesColl.FindOne(context.Background(), bson.M{
		"enterprise_id": peer.EnterpriseID,
	}).Decode(&device)

	if err != nil {
		return "", fmt.Errorf("failed to get device: %v", err)
	}

	// Join the AllowedIPs slice into a comma-separated string without the square brackets
	allowedIPsStr := strings.Join(peer.AllowedIPs, ", ")

	// 生成配置
	config := fmt.Sprintf(`
[Interface]
Address = %s/24
PrivateKey = %s
DNS = %s

[Peer]
PublicKey = %s
PresharedKey = %s
AllowedIPs = %s
PersistentKeepalive = 0
Endpoint = %s
	`,
		peer.IP,
		peer.PrivateKey,
		peer.DNS,
		device.PublicKey,
		peer.PresharedKey,
		allowedIPsStr,
		device.Endpoint,
	)

	return config, nil
}

// WriteFile writes data to a file with proper locking
func (h *WGHelper) WriteFile(filename string, data []byte, perm os.FileMode) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return os.WriteFile(filename, data, perm)
}

// ReadFile reads a file with proper locking
func (h *WGHelper) ReadFile(filename string) ([]byte, error) {
	h.mu.Lock()
	defer h.mu.Unlock()
	return os.ReadFile(filename)
}

// DeleteFile removes a file with proper locking
func (h *WGHelper) DeleteFile(filename string) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return os.Remove(filename)
}

// SyncConfig synchronizes the configuration file with the running interface
func (h *WGHelper) SyncConfig(iface string) error {
	// Check if interface exists and is running
	if !h.IsInterfaceRunning(iface) {
		return fmt.Errorf("interface %s is not running", iface)
	}

	h.mu.Lock()
	configFile := filepath.Join(h.configPath, fmt.Sprintf("%s.conf", iface))
	h.mu.Unlock()

	// Create a shell command to strip the config and apply it
	// This avoids creating a temporary file that might leak sensitive information
	cmd := exec.Command("bash", "-c", fmt.Sprintf("wg syncconf %s <(wg-quick strip %s)", iface, configFile))
	output, err := cmd.CombinedOutput()

	if err != nil {
		return fmt.Errorf("failed to sync configuration: %v, output: %s", err, output)
	}

	log.Printf("Successfully synchronized configuration for interface %s", iface)
	return nil
}

// StartInterface starts a WireGuard interface
func (h *WGHelper) StartInterface(iface string) error {
	cmd := exec.Command("wg-quick", "up", iface)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to start interface %s: %v, output: %s", iface, err, output)
	}
	return nil
}

// StopInterface stops a WireGuard interface
func (h *WGHelper) StopInterface(iface string) error {
	cmd := exec.Command("wg-quick", "down", iface)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to stop interface %s: %v, output: %s", iface, err, output)
	}
	return nil
}

// RestartInterface restarts a WireGuard interface
func (h *WGHelper) RestartInterface(iface string) error {
	if err := h.StopInterface(iface); err != nil {
		return err
	}

	// Small delay to ensure interface is fully down
	time.Sleep(500 * time.Millisecond)

	return h.StartInterface(iface)
}

// IsInterfaceRunning checks if a WireGuard interface is running
func (h *WGHelper) IsInterfaceRunning(iface string) bool {
	cmd := exec.Command("wg", "show", iface)
	err := cmd.Run()
	return err == nil
}

// -------------------- Generate QR Code --------------------

// GeneratePeerQRCodeBase64 -> generate peer qr code base64
func (h *WGHelper) GeneratePeerQRCodeBase64(data string) (string, error) {
	qr, err := qrcode.New(data, qrcode.Medium)
	if err != nil {
		return "", fmt.Errorf("failed to generate QR code: %v", err)
	}

	// generate png data
	png, err := qr.PNG(256)
	if err != nil {
		return "", fmt.Errorf("failed to generate PNG: %v", err)
	}

	// convert to base64
	return "data:image/png;base64," + base64.StdEncoding.EncodeToString(png), nil
}
