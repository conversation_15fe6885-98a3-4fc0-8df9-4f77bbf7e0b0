package wireguardmgr

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/skip2/go-qrcode"
	"go.mongodb.org/mongo-driver/bson"
)

// WGHelper provides utility functions for WireGuard management using command line tools
type WGHelper struct {
	configPath string
	// Mutex for file operations to prevent concurrent writes
	mu sync.Mutex
	// Namespace manager for network isolation
	nsMgr *NamespaceManager
}

// NewWGHelper creates a new WireGuard helper
func NewWGHelper(configPath string) *WGHelper {
	if configPath == "" {
		configPath = wgConfigPath
	}
	return &WGHelper{
		configPath: configPath,
		nsMgr:      NewNamespaceManager(),
	}
}

// generateServerConfig generates server-side WireGuard configuration file
func (m *Manager) generateServerConfig(device *Device) error {
	configPath := fmt.Sprintf("%s/%s.conf", wgConfigPath, device.Name)

	// 获取该设备的所有 peers
	peers, err := m.ListPeers(context.Background(), device.EnterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get peers: %v", err)
	}

	// 生成基础配置
	config := fmt.Sprintf(`[Interface]
PrivateKey = %s
Address = %s
ListenPort = %d
MTU=1420
PreUp = 
PostUp = iptables -t nat -A POSTROUTING -s %s -o %s -j MASQUERADE; iptables -A INPUT -p udp -m udp --dport %d -j ACCEPT; iptables -A FORWARD -i %s -j ACCEPT; iptables -A FORWARD -o %s -j ACCEPT;
PreDown = 
PostDown = iptables -t nat -D POSTROUTING -s %s -o %s -j MASQUERADE; iptables -D INPUT -p udp -m udp --dport %d -j ACCEPT; iptables -D FORWARD -i %s -j ACCEPT; iptables -D FORWARD -o %s -j ACCEPT;
`,
		device.PrivateKey,
		device.IP,
		device.ListenPort,
		strings.TrimSuffix(device.IP, "1/24")+"0/24", // 子网
		config.AppConfig.NetInterface,
		device.ListenPort,
		device.Name,
		device.Name,
		strings.TrimSuffix(device.IP, "1/24")+"0/24", // 子网
		config.AppConfig.NetInterface,
		device.ListenPort,
		device.Name,
		device.Name,
	)

	// 添加所有 peers 配置
	for _, peer := range peers {
		// 获取该peer的额外路由信息
		var additionalRoutes []string
		cursor, err := db.MongoDB.Collection(PeerRoutesCollection).Find(context.Background(),
			bson.M{"peer_id": peer.ID, "enterprise_id": device.EnterpriseID})

		if err == nil {
			defer cursor.Close(context.Background())

			var route struct {
				Network string `bson:"network"`
			}

			for cursor.Next(context.Background()) {
				if err := cursor.Decode(&route); err == nil {
					additionalRoutes = append(additionalRoutes, route.Network)
				}
			}
		}

		// 构建AllowedIPs字段
		allowedIPs := []string{fmt.Sprintf("%s/32", peer.IP)}
		allowedIPs = append(allowedIPs, additionalRoutes...)

		peerConfig := fmt.Sprintf(`
# Client: %s (%s)
[Peer]
PublicKey = %s
PresharedKey = %s
AllowedIPs = %s
`,
			peer.Name,
			peer.ID.String(),
			peer.PublicKey,
			peer.PresharedKey,
			strings.Join(allowedIPs, ", "),
		)
		config += peerConfig
	}

	return m.Helper.WriteFile(configPath, []byte(config), 0600)
}

// GeneratePeerConfig 生成对等点配置
func (m *Manager) GeneratePeerConfig(peer *Peer) (string, error) {
	// 获取设备信息
	var device Device
	err := m.DevicesColl.FindOne(context.Background(), bson.M{
		"enterprise_id": peer.EnterpriseID,
	}).Decode(&device)

	if err != nil {
		return "", fmt.Errorf("failed to get device: %v", err)
	}

	// Join the AllowedIPs slice into a comma-separated string without the square brackets
	allowedIPsStr := strings.Join(peer.AllowedIPs, ", ")

	// 生成配置
	config := fmt.Sprintf(`
[Interface]
Address = %s/24
PrivateKey = %s
DNS = %s

[Peer]
PublicKey = %s
PresharedKey = %s
AllowedIPs = %s
PersistentKeepalive = 0
Endpoint = %s
	`,
		peer.IP,
		peer.PrivateKey,
		peer.DNS,
		device.PublicKey,
		peer.PresharedKey,
		allowedIPsStr,
		device.Endpoint,
	)

	return config, nil
}

// WriteFile writes data to a file with proper locking
func (h *WGHelper) WriteFile(filename string, data []byte, perm os.FileMode) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return os.WriteFile(filename, data, perm)
}

// ReadFile reads a file with proper locking
func (h *WGHelper) ReadFile(filename string) ([]byte, error) {
	h.mu.Lock()
	defer h.mu.Unlock()
	return os.ReadFile(filename)
}

// DeleteFile removes a file with proper locking
func (h *WGHelper) DeleteFile(filename string) error {
	h.mu.Lock()
	defer h.mu.Unlock()
	return os.Remove(filename)
}

// SyncConfig synchronizes the configuration file with the running interface
func (h *WGHelper) SyncConfig(iface, namespace string) error {
	// Check if interface exists and is running
	if !h.IsInterfaceRunningInNamespace(iface, namespace) {
		return fmt.Errorf("interface %s is not running in namespace %s", iface, namespace)
	}

	h.mu.Lock()
	configFile := filepath.Join(h.configPath, fmt.Sprintf("%s.conf", iface))
	h.mu.Unlock()

	// Create a shell command to strip the config and apply it within the namespace
	var cmd *exec.Cmd
	if namespace != "" {
		// Execute in namespace
		cmd = exec.Command("bash", "-c", fmt.Sprintf("ip netns exec %s bash -c 'wg syncconf %s <(wg-quick strip %s)'", namespace, iface, configFile))
	} else {
		// Execute in root namespace (backward compatibility)
		cmd = exec.Command("bash", "-c", fmt.Sprintf("wg syncconf %s <(wg-quick strip %s)", iface, configFile))
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to sync configuration: %v, output: %s", err, output)
	}

	log.Printf("Successfully synchronized configuration for interface %s in namespace %s", iface, namespace)
	return nil
}

// StartInterface starts a WireGuard interface in a specific namespace
func (h *WGHelper) StartInterface(iface, namespace string) error {
	var cmd *exec.Cmd
	if namespace != "" {
		// Start interface in namespace
		cmd = exec.Command("ip", "netns", "exec", namespace, "wg-quick", "up", iface)
	} else {
		// Start interface in root namespace (backward compatibility)
		cmd = exec.Command("wg-quick", "up", iface)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to start interface %s in namespace %s: %v, output: %s", iface, namespace, err, output)
	}

	log.Printf("Successfully started interface %s in namespace %s", iface, namespace)
	return nil
}

// StopInterface stops a WireGuard interface in a specific namespace
func (h *WGHelper) StopInterface(iface, namespace string) error {
	var cmd *exec.Cmd
	if namespace != "" {
		// Stop interface in namespace
		cmd = exec.Command("ip", "netns", "exec", namespace, "wg-quick", "down", iface)
	} else {
		// Stop interface in root namespace (backward compatibility)
		cmd = exec.Command("wg-quick", "down", iface)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to stop interface %s in namespace %s: %v, output: %s", iface, namespace, err, output)
	}

	log.Printf("Successfully stopped interface %s in namespace %s", iface, namespace)
	return nil
}

// RestartInterface restarts a WireGuard interface in a specific namespace
func (h *WGHelper) RestartInterface(iface, namespace string) error {
	if err := h.StopInterface(iface, namespace); err != nil {
		return err
	}

	// Small delay to ensure interface is fully down
	time.Sleep(500 * time.Millisecond)

	return h.StartInterface(iface, namespace)
}

// IsInterfaceRunning checks if a WireGuard interface is running (backward compatibility)
func (h *WGHelper) IsInterfaceRunning(iface string) bool {
	return h.IsInterfaceRunningInNamespace(iface, "")
}

// IsInterfaceRunningInNamespace checks if a WireGuard interface is running in a specific namespace
func (h *WGHelper) IsInterfaceRunningInNamespace(iface, namespace string) bool {
	var cmd *exec.Cmd
	if namespace != "" {
		cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "show", iface)
	} else {
		cmd = exec.Command("wg", "show", iface)
	}

	err := cmd.Run()
	return err == nil
}

// -------------------- Generate QR Code --------------------

// GeneratePeerQRCodeBase64 -> generate peer qr code base64
func (h *WGHelper) GeneratePeerQRCodeBase64(data string) (string, error) {
	qr, err := qrcode.New(data, qrcode.Medium)
	if err != nil {
		return "", fmt.Errorf("failed to generate QR code: %v", err)
	}

	// generate png data
	png, err := qr.PNG(256)
	if err != nil {
		return "", fmt.Errorf("failed to generate PNG: %v", err)
	}

	// convert to base64
	return "data:image/png;base64," + base64.StdEncoding.EncodeToString(png), nil
}

// -------------------- Namespace-aware WireGuard Information Retrieval --------------------

// WGDeviceInfo represents WireGuard device information
type WGDeviceInfo struct {
	Name       string
	PublicKey  string
	ListenPort int
	Peers      []WGPeerInfo
}

// WGPeerInfo represents WireGuard peer information
type WGPeerInfo struct {
	PublicKey     string
	Endpoint      string
	AllowedIPs    []string
	LastHandshake time.Time
	RxBytes       int64
	TxBytes       int64
}

// GetDeviceInfoInNamespace retrieves WireGuard device information from a specific namespace
func (h *WGHelper) GetDeviceInfoInNamespace(iface, namespace string) (*WGDeviceInfo, error) {
	var cmd *exec.Cmd
	if namespace != "" {
		cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "show", iface, "dump")
	} else {
		cmd = exec.Command("wg", "show", iface, "dump")
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to get device info for %s in namespace %s: %v", iface, namespace, err)
	}

	return h.parseWGDump(string(output))
}

// parseWGDump parses the output of 'wg show <interface> dump'
func (h *WGHelper) parseWGDump(output string) (*WGDeviceInfo, error) {
	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) == 0 {
		return nil, fmt.Errorf("empty wg dump output")
	}

	device := &WGDeviceInfo{
		Peers: make([]WGPeerInfo, 0),
	}

	for i, line := range lines {
		fields := strings.Split(line, "\t")
		if len(fields) < 3 {
			continue
		}

		if i == 0 {
			// First line is device info
			device.PublicKey = fields[1]
			if len(fields) > 2 && fields[2] != "(none)" {
				fmt.Sscanf(fields[2], "%d", &device.ListenPort)
			}
		} else {
			// Subsequent lines are peer info
			peer := WGPeerInfo{
				PublicKey: fields[1],
			}

			// Parse endpoint
			if len(fields) > 3 && fields[3] != "(none)" {
				peer.Endpoint = fields[3]
			}

			// Parse allowed IPs
			if len(fields) > 4 && fields[4] != "(none)" {
				peer.AllowedIPs = strings.Split(fields[4], ",")
				for j := range peer.AllowedIPs {
					peer.AllowedIPs[j] = strings.TrimSpace(peer.AllowedIPs[j])
				}
			}

			// Parse last handshake
			if len(fields) > 5 && fields[5] != "0" {
				var timestamp int64
				if _, err := fmt.Sscanf(fields[5], "%d", &timestamp); err == nil {
					// Convert Unix timestamp to time.Time
					peer.LastHandshake = time.Unix(timestamp, 0)
				}
			}

			// Parse received bytes
			if len(fields) > 6 {
				fmt.Sscanf(fields[6], "%d", &peer.RxBytes)
			}

			// Parse transmitted bytes
			if len(fields) > 7 {
				fmt.Sscanf(fields[7], "%d", &peer.TxBytes)
			}

			device.Peers = append(device.Peers, peer)
		}
	}

	return device, nil
}

// GetAllDevicesInNamespace lists all WireGuard devices in a specific namespace
func (h *WGHelper) GetAllDevicesInNamespace(namespace string) ([]string, error) {
	var cmd *exec.Cmd
	if namespace != "" {
		cmd = exec.Command("ip", "netns", "exec", namespace, "wg", "show", "interfaces")
	} else {
		cmd = exec.Command("wg", "show", "interfaces")
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("failed to list interfaces in namespace %s: %v", namespace, err)
	}

	interfaces := strings.Fields(strings.TrimSpace(string(output)))
	return interfaces, nil
}

// CreateDeviceInNamespace creates a WireGuard device in a specific namespace
func (h *WGHelper) CreateDeviceInNamespace(device *Device, hostInterface string) error {
	// Generate namespace name
	nsName := h.nsMgr.GenerateNamespaceName(device.Name, device.EnterpriseID)

	// Create namespace
	if err := h.nsMgr.CreateNamespace(nsName); err != nil {
		return fmt.Errorf("failed to create namespace: %v", err)
	}

	// Set up loopback interface in namespace
	if err := h.nsMgr.SetupNamespaceLoopback(nsName); err != nil {
		return fmt.Errorf("failed to setup loopback: %v", err)
	}

	// Set up networking for external connectivity
	if err := h.nsMgr.SetupNamespaceNetworking(nsName, device.ListenPort, hostInterface); err != nil {
		// Cleanup namespace if networking setup fails
		h.nsMgr.DeleteNamespace(nsName)
		return fmt.Errorf("failed to setup namespace networking: %v", err)
	}

	// Update device with namespace information
	device.Namespace = nsName

	log.Printf("Successfully created namespace %s for device %s with networking", nsName, device.Name)
	return nil
}

// DeleteDeviceFromNamespace removes a WireGuard device and its namespace
func (h *WGHelper) DeleteDeviceFromNamespace(device *Device, hostInterface string) error {
	if device.Namespace == "" {
		log.Printf("Device %s has no namespace, skipping namespace cleanup", device.Name)
		return nil
	}

	// Stop interface first
	if err := h.StopInterface(device.Name, device.Namespace); err != nil {
		log.Printf("Warning: failed to stop interface %s: %v", device.Name, err)
	}

	// Clean up networking configuration
	if err := h.nsMgr.CleanupNamespaceNetworking(device.Namespace, device.ListenPort, hostInterface); err != nil {
		log.Printf("Warning: failed to cleanup namespace networking: %v", err)
	}

	// Delete namespace
	if err := h.nsMgr.DeleteNamespace(device.Namespace); err != nil {
		return fmt.Errorf("failed to delete namespace %s: %v", device.Namespace, err)
	}

	log.Printf("Successfully deleted namespace %s for device %s", device.Namespace, device.Name)
	return nil
}

// GenerateKeys generates a new WireGuard key pair
func (h *WGHelper) GenerateKeys() (privateKey, publicKey string, err error) {
	// Generate private key
	cmd := exec.Command("wg", "genkey")
	privateKeyBytes, err := cmd.Output()
	if err != nil {
		return "", "", fmt.Errorf("failed to generate private key: %v", err)
	}
	privateKey = strings.TrimSpace(string(privateKeyBytes))

	// Generate public key from private key
	cmd = exec.Command("wg", "pubkey")
	cmd.Stdin = strings.NewReader(privateKey)
	publicKeyBytes, err := cmd.Output()
	if err != nil {
		return "", "", fmt.Errorf("failed to generate public key: %v", err)
	}
	publicKey = strings.TrimSpace(string(publicKeyBytes))

	return privateKey, publicKey, nil
}

// GeneratePresharedKey generates a new WireGuard preshared key
func (h *WGHelper) GeneratePresharedKey() (string, error) {
	cmd := exec.Command("wg", "genpsk")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to generate preshared key: %v", err)
	}
	return strings.TrimSpace(string(output)), nil
}
