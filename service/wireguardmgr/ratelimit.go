package wireguardmgr

import (
	"fmt"
	"net"
	"os/exec"
	"strings"

	"github.com/florianl/go-tc"
	"github.com/florianl/go-tc/core"
	"golang.org/x/sys/unix"
)

// RateLimit 定义带宽限制参数
type RateLimit struct {
	Rate     uint32 `json:"rate" bson:"rate"`           // 速率，单位为 kbps (kilobits per second)
	Burst    uint32 `json:"burst" bson:"burst"`         // 突发大小，单位为 kb (kilobytes)
	Latency  uint32 `json:"latency" bson:"latency"`     // 最大延迟，单位为毫秒
	MTU      uint32 `json:"mtu" bson:"mtu"`             // 最大传输单元，单位为字节
	PeakRate uint32 `json:"peak_rate" bson:"peak_rate"` // 峰值速率，单位为 kbps (kilobits per second)，0表示不限制
	MinBurst uint32 `json:"min_burst" bson:"min_burst"` // 最小突发大小，单位为字节
}

// DefaultRateLimit 返回默认的限速参数
func DefaultRateLimit(rate uint32) RateLimit {
	return RateLimit{
		Rate:     rate,
		Burst:    rate / 8, // 默认突发大小为速率的1/8秒数据量
		Latency:  50,       // 默认最大延迟50ms
		MTU:      1500,     // 默认MTU 1500字节
		PeakRate: 0,        // 默认不限制峰值速率
		MinBurst: 0,        // 默认不设置最小突发大小
	}
}

// LimitInterfaceBandwidth 对指定接口应用带宽限制
// rate: 速率，单位为 kbps (kilobits per second)
// 此函数不保存带宽限制到数据库，只应用到接口。建议使用 SaveAndApplyRateLimit 函数。
func LimitInterfaceBandwidth(iface string, rate uint32) error {
	return ApplyRateLimit(iface, DefaultRateLimit(rate))
}

// ApplyRateLimit 应用自定义的带宽限制
// 此函数不保存带宽限制到数据库，只应用到接口。建议使用 SaveAndApplyRateLimit 函数。
func ApplyRateLimit(iface string, limit RateLimit) error {
	// 创建 netlink 连接
	rtnl, err := tc.Open(&tc.Config{})
	if err != nil {
		return fmt.Errorf("failed to open rtnetlink connection: %v", err)
	}
	defer rtnl.Close()

	// 获取接口索引
	iface_obj, err := net.InterfaceByName(iface)
	if err != nil {
		return fmt.Errorf("failed to get interface index: %v", err)
	}
	index := uint32(iface_obj.Index)

	// 将 kbps 转换为 bps
	rateBps := limit.Rate * 1000 / 8 // 从 kbps 转换为 bytes/s
	burstBytes := limit.Burst * 1000 // 从 kb 转换为 bytes

	// RateSpec for TBF
	rateSpec := tc.RateSpec{
		Rate:      rateBps,
		Linklayer: 1, // Ethernet
	}

	peakRateSpec := tc.RateSpec{}
	if limit.PeakRate > 0 {
		peakRateBytes := limit.PeakRate * 1000 / 8
		peakRateSpec = tc.RateSpec{
			Rate:      peakRateBytes,
			Linklayer: 1, // Ethernet
		}
	}

	// 创建 TBF qdisc
	tbfQdisc := &tc.Tbf{
		Parms: &tc.TbfQopt{
			Rate:   rateSpec,
			Limit:  burstBytes,
			Buffer: burstBytes,
			Mtu:    limit.MTU,
		},
		Burst: &burstBytes,
	}

	// 如果设置了峰值速率
	if limit.PeakRate > 0 {
		tbfQdisc.Parms.PeakRate = peakRateSpec

		if limit.MinBurst > 0 {
			minBurst := limit.MinBurst
			tbfQdisc.Pburst = &minBurst
		}
	}

	qdisc := tc.Object{
		Msg: tc.Msg{
			Family:  unix.AF_UNSPEC,
			Ifindex: index,
			Handle:  core.BuildHandle(tc.HandleRoot, 0),
			Parent:  tc.HandleRoot,
			Info:    0,
		},
		Attribute: tc.Attribute{
			Kind: "tbf",
			Tbf:  tbfQdisc,
		},
	}

	// 添加 qdisc
	if err := rtnl.Qdisc().Add(&qdisc); err != nil {
		return fmt.Errorf("failed to add tbf qdisc: %v", err)
	}

	return nil
}

// RemoveRateLimit 移除接口的带宽限制
// 此函数不从数据库中删除带宽限制记录，只从接口上移除。建议使用 RemoveAndDeleteRateLimit 函数。
func RemoveRateLimit(iface string) error {
	// 使用系统命令直接删除 qdisc
	cmd := exec.Command("tc", "qdisc", "del", "dev", iface, "root")
	output, err := cmd.CombinedOutput()
	
	// 如果命令执行失败但输出包含"No such file or directory"或"RTNETLINK answers: No such file or directory"，
	// 则认为是接口不存在，这种情况下不应该返回错误
	if err != nil {
		outputStr := string(output)
		if strings.Contains(outputStr, "No such file or directory") || 
		   strings.Contains(outputStr, "RTNETLINK answers: No such file or directory") ||
		   strings.Contains(outputStr, "Cannot find device") {
			return nil // 接口可能不存在，不视为错误
		}
		return fmt.Errorf("failed to remove qdisc: %v, output: %s", err, outputStr)
	}

	return nil
}

// GetRateLimit 获取接口当前的带宽限制
// 此函数从接口上获取实际应用的带宽限制，如果接口不存在或没有带宽限制，返回错误。
// 要获取数据库中存储的带宽限制，请使用 GetStoredRateLimit 函数。
func GetRateLimit(iface string) (*RateLimit, error) {
	// 创建 netlink 连接
	rtnl, err := tc.Open(&tc.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to open rtnetlink connection: %v", err)
	}
	defer rtnl.Close()

	// 获取接口索引
	iface_obj, err := net.InterfaceByName(iface)
	if err != nil {
		return nil, fmt.Errorf("failed to get interface index: %v", err)
	}
	index := uint32(iface_obj.Index)

	// 获取 qdisc
	qdiscs, err := rtnl.Qdisc().Get()
	if err != nil {
		return nil, fmt.Errorf("failed to get qdiscs: %v", err)
	}

	// 查找指定接口的 TBF qdisc
	for _, qdisc := range qdiscs {
		if qdisc.Ifindex == index && qdisc.Kind == "tbf" {
			if qdisc.Tbf == nil {
				return nil, fmt.Errorf("tbf qdisc found but attributes are nil")
			}

			if qdisc.Tbf.Parms == nil {
				return nil, fmt.Errorf("tbf qdisc found but parms are nil")
			}

			// 从 bytes/s 转换为 kbps
			rateKbps := qdisc.Tbf.Parms.Rate.Rate * 8 / 1000
			burstKb := uint32(0)
			if qdisc.Tbf.Burst != nil {
				burstKb = *qdisc.Tbf.Burst / 1000
			}

			limit := &RateLimit{
				Rate:    rateKbps,
				Burst:   burstKb,
				MTU:     qdisc.Tbf.Parms.Mtu,
				Latency: 0, // Latency is not directly accessible from TbfQopt in this version
			}

			if qdisc.Tbf.Parms.PeakRate.Rate > 0 {
				limit.PeakRate = qdisc.Tbf.Parms.PeakRate.Rate * 8 / 1000
				if qdisc.Tbf.Pburst != nil {
					limit.MinBurst = *qdisc.Tbf.Pburst
				}
			}

			return limit, nil
		}
	}

	return nil, fmt.Errorf("no tbf qdisc found for interface %s", iface)
}

// SaveAndApplyRateLimit 保存带宽限制配置到数据库并应用到接口
// 这是推荐的应用带宽限制的方法，确保设置在系统重启后仍然可以恢复。
func SaveAndApplyRateLimit(iface string, enterpriseID string, limit RateLimit) error {
	// 首先应用到接口
	if err := ApplyRateLimit(iface, limit); err != nil {
		return fmt.Errorf("failed to apply rate limit: %v", err)
	}

	// 然后保存到数据库
	if err := StoreRateLimit(iface, enterpriseID, limit); err != nil {
		// 尝试回滚接口上的设置
		RemoveRateLimit(iface)
		return fmt.Errorf("failed to store rate limit: %v", err)
	}

	return nil
}

// RemoveAndDeleteRateLimit 从接口移除带宽限制并从数据库删除相关记录
// 这是推荐的移除带宽限制的方法，确保系统状态和数据库状态一致。
func RemoveAndDeleteRateLimit(iface string, enterpriseID string) error {
	// 先从接口移除
	if err := RemoveRateLimit(iface); err != nil {
		return fmt.Errorf("failed to remove rate limit from interface: %v", err)
	}

	// 然后从数据库删除
	if err := DeleteRateLimit(iface, enterpriseID); err != nil {
		return fmt.Errorf("failed to delete rate limit from database: %v", err)
	}

	return nil
}

// GetCurrentRateLimit 获取当前的带宽限制信息，优先从接口获取，如果接口上没有设置，则尝试从数据库获取
func GetCurrentRateLimit(iface string, enterpriseID string) (*RateLimit, error, bool) {
	// 先尝试从接口获取
	limit, err := GetRateLimit(iface)
	if err == nil {
		return limit, nil, true // 返回接口上的限制，第三个参数表示来源是接口
	}

	// 如果接口上获取失败，尝试从数据库获取
	limit, err = GetStoredRateLimit(iface, enterpriseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get rate limit from database: %v", err), false
	}

	if limit == nil {
		return nil, nil, false // 数据库中也没有配置
	}

	return limit, nil, false // 返回数据库中的配置，第三个参数表示来源不是接口
}

// EnsureRateLimitApplied 确保带宽限制被应用，如果接口上未设置但数据库中有配置，则应用数据库中的配置
func EnsureRateLimitApplied(iface string, enterpriseID string) error {
	limit, err, fromInterface := GetCurrentRateLimit(iface, enterpriseID)
	if err != nil || limit == nil {
		return nil // 没有配置，不需要应用
	}

	if !fromInterface {
		// 数据库中有配置但接口上没有设置，应用到接口
		if err := ApplyRateLimit(iface, *limit); err != nil {
			return fmt.Errorf("failed to apply stored rate limit: %v", err)
		}
	}

	return nil
}
