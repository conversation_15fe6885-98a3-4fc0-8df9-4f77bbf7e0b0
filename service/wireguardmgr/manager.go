package wireguardmgr

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.zx2c4.com/wireguard/wgctrl"
	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

// Manager wraps MongoDB client and operations on device collections
type Manager struct {
	DevicesColl *mongo.Collection
	PeersColl   *mongo.Collection
	Helper      *WGHelper
	ipam        *IPAM
}

// NewManager creates a new Manager based on MongoDB URI and database name
func NewManager() *Manager {
	return &Manager{
		DevicesColl: db.MongoDB.Collection(DeviceCollection),
		PeersColl:   db.MongoDB.Collection(PeerCollection),
		Helper:      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(wgConfigPath),
		ipam:        NewIPAM(),
	}
}

// -------------------- Device CRUD Operations --------------------

// CreateDevice creates a device and generates configuration file
func (m *Manager) CreateDevice(ctx context.Context, device *Device) error {
	log.Printf("Starting to create device: %s for enterprise: %s", device.Name, device.EnterpriseID)

	// Allocate IP address
	log.Printf("Allocating subnet for enterprise: %s", device.EnterpriseID)
	enterpriseIPAM, err := m.ipam.AllocateEnterpriseSubnet(device.EnterpriseID)
	if err != nil {
		return fmt.Errorf("failed to allocate subnet: %v", err)
	}

	log.Printf("Allocating IP from subnet")
	deviceIP, err := enterpriseIPAM.AllocateIP()
	if err != nil {
		return fmt.Errorf("failed to allocate IP: %v", err)
	}
	device.IP = deviceIP + "/24"
	log.Printf("Allocated IP: %s", device.IP)

	device.ID = primitive.NewObjectID()
	device.Endpoint = fmt.Sprintf("%s:%d", config.AppConfig.Host, device.ListenPort)
	privateKey, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		enterpriseIPAM.ReleaseIP(deviceIP)
		return err
	}
	device.PrivateKey = privateKey.String()
	device.PublicKey = privateKey.PublicKey().String()
	device.CreatedAt = time.Now()

	// Insert into database first
	_, err = m.DevicesColl.InsertOne(ctx, device)
	if err != nil {
		enterpriseIPAM.ReleaseIP(deviceIP)
		return err
	}

	// Generate config file after successful database insertion
	if err := m.generateServerConfig(device); err != nil {
		// Rollback database operation if config file generation fails
		_, deleteErr := m.DevicesColl.DeleteOne(ctx, bson.M{"_id": device.ID})
		if deleteErr != nil {
			log.Printf("Warning: failed to rollback device creation: %v", deleteErr)
		}
		enterpriseIPAM.ReleaseIP(deviceIP)
		return fmt.Errorf("failed to generate server config: %v", err)
	}

	m.Helper.StartInterface(device.Name)

	// 如果指定了带宽限制，则应用它
	if device.BandwidthLimit > 0 {
		rateLimit := DefaultRateLimit(device.BandwidthLimit)
		if err := SaveAndApplyRateLimit(device.Name, device.EnterpriseID, rateLimit); err != nil {
			log.Printf("Warning: failed to apply bandwidth limit: %v", err)
		}
	}

	return nil
}

func (m *Manager) GetDevice(ctx context.Context, enterpriseID string) (*Device, error) {
	var device Device
	err := m.DevicesColl.FindOne(ctx, bson.M{"enterprise_id": enterpriseID}).Decode(&device)
	if err != nil {
		return nil, err
	}
	return &device, nil
}

func (m *Manager) UpdateDevice(ctx context.Context, device *Device) error {
	filter := bson.M{"_id": device.ID}
	update := bson.M{"$set": bson.M{
		"name": device.Name,
		"ip":   device.IP,
	}}
	_, err := m.DevicesColl.UpdateOne(ctx, filter, update)
	return err
}

func (m *Manager) DeleteDevice(ctx context.Context, name string, enterpriseID string) error {
	// 先获取设备信息，以便后续删除相关的peers
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 删除设备
	_, err = m.DevicesColl.DeleteOne(ctx, bson.M{"name": name, "enterprise_id": enterpriseID})
	if err != nil {
		return fmt.Errorf("failed to delete device: %v", err)
	}

	// 删除关联的所有peers
	_, err = m.PeersColl.DeleteMany(ctx, bson.M{"enterprise_id": enterpriseID})
	if err != nil {
		log.Printf("Warning: failed to delete associated peers: %v", err)
	}

	// 移除带宽限制（如果有）并从数据库删除记录
	RemoveAndDeleteRateLimit(name, enterpriseID)

	// 停止并删除WireGuard配置
	if err := m.Helper.StopInterface(name); err != nil {
		log.Printf("Warning: failed to stop WireGuard interface: %v", err)
	}
	configPath := fmt.Sprintf("%s/%s.conf", wgConfigPath, name)
	if err := m.Helper.DeleteFile(configPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove config file: %v", err)
	}

	// 释放设备和peers使用的IP地址
	if enterpriseIPAM, err := m.ipam.GetEnterpriseIPAM(enterpriseID); err == nil {
		deviceIP := strings.TrimSuffix(device.IP, "/24")
		enterpriseIPAM.ReleaseIP(deviceIP)

		// 释放所有peer的IP
		peers, err := m.ListPeers(ctx, enterpriseID)
		if err == nil {
			for _, peer := range peers {
				enterpriseIPAM.ReleaseIP(peer.IP)
			}
		}
	}

	// 删除IPAM记录
	_, err = db.MongoDB.Collection("ipam_records").DeleteOne(ctx,
		bson.M{"enterprise_id": enterpriseID})
	if err != nil {
		return fmt.Errorf("failed to delete IPAM record: %v", err)
	}

	// 删除采集记录
	_, err = db.Taos.Exec(fmt.Sprintf("DROP TABLE IF EXISTS wgstats.%s", device.Name))
	if err != nil {
		return fmt.Errorf("failed to delete wgstats record: %v", err)
	}

	return nil
}

func (m *Manager) ListDevices(ctx context.Context, limit int, page int) ([]Device, int64, error) {
	// 确保 limit 和 page 的值有效
	if limit <= 0 {
		limit = 10 // 默认每页显示10条
	}
	if page <= 0 {
		page = 1 // 默认第一页
	}

	// 获取总数
	total, err := m.DevicesColl.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count devices: %v", err)
	}

	skip := (page - 1) * limit

	// 使用 Find 方法时添加 Skip 和 Limit
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit))

	cursor, err := m.DevicesColl.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var devices []Device
	if err := cursor.All(ctx, &devices); err != nil {
		return nil, 0, err
	}

	return devices, total, nil
}

// -------------------- Peer CRUD 操作 --------------------

// CreatePeer 创建一个新的 Peer，自动配置相关参数
func (m *Manager) CreatePeer(ctx context.Context, deviceName string, enterpriseID string, peerName string) (*Peer, error) {
	// 获取关联的设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %v", err)
	}

	// 从设备 IP 获取子网信息
	_, deviceNet, err := net.ParseCIDR(device.IP)
	if err != nil {
		return nil, fmt.Errorf("failed to parse device IP: %v", err)
	}

	// 获取企业 IPAM
	enterpriseIPAM, err := m.ipam.GetEnterpriseIPAM(enterpriseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get enterprise IPAM: %v", err)
	}

	// 分配 IP
	peerIP, err := enterpriseIPAM.AllocateIP()
	if err != nil {
		return nil, fmt.Errorf("failed to allocate IP: %v", err)
	}

	// 创建新的peer
	peer := &Peer{
		ID:           primitive.NewObjectID(),
		Name:         peerName,
		EnterpriseID: enterpriseID,
		IP:           peerIP,
		CreatedAt:    time.Now(),
		DNS:          "*******,***************",
	}

	// 生成密钥对
	privateKey, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		enterpriseIPAM.ReleaseIP(peerIP) // 释放已分配的 IP
		return nil, fmt.Errorf("failed to generate private key: %v", err)
	}
	peer.PrivateKey = privateKey.String()
	peer.PublicKey = privateKey.PublicKey().String()

	// 生成预共享密钥
	presharedKey, err := wgtypes.GenerateKey()
	if err != nil {
		enterpriseIPAM.ReleaseIP(peerIP)
		return nil, fmt.Errorf("failed to generate preshared key: %v", err)
	}
	peer.PresharedKey = presharedKey.String()

	// 设置 AllowedIPs（允许访问整个子网）
	peer.AllowedIPs = []string{
		deviceNet.String(), // 整个子网
	}

	// 将peer插入到wg_peers集合
	_, err = m.PeersColl.InsertOne(ctx, peer)
	if err != nil {
		enterpriseIPAM.ReleaseIP(peerIP)
		return nil, fmt.Errorf("failed to insert peer: %v", err)
	}

	// 更新 WireGuard 配置文件
	if err := m.generateServerConfig(device); err != nil {
		// 回滚数据库操作
		m.PeersColl.DeleteOne(ctx, bson.M{"_id": peer.ID})
		enterpriseIPAM.ReleaseIP(peerIP)
		return nil, fmt.Errorf("failed to update server config: %v", err)
	}

	err = m.Helper.SyncConfig(device.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to sync config: %v", err)
	}

	return peer, nil
}

func (m *Manager) GetPeer(ctx context.Context, enterpriseID string, peerID primitive.ObjectID) (*Peer, error) {
	var peer Peer
	err := m.PeersColl.FindOne(ctx, bson.M{
		"_id":           peerID,
		"enterprise_id": enterpriseID,
	}).Decode(&peer)

	if err != nil {
		return nil, fmt.Errorf("failed to get peer: %v", err)
	}

	return &peer, nil
}

func (m *Manager) UpdatePeer(ctx context.Context, deviceName string, enterpriseID string, peer *Peer) error {
	// 更新peer
	update := bson.M{
		"$set": bson.M{
			"name":          peer.Name,
			"dns":           peer.DNS,
			"allowed_ips":   peer.AllowedIPs,
			"preshared_key": peer.PresharedKey,
		},
	}

	result, err := m.PeersColl.UpdateOne(
		ctx,
		bson.M{
			"_id":           peer.ID,
			"enterprise_id": enterpriseID,
		},
		update,
	)

	if err != nil {
		return fmt.Errorf("failed to update peer: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("peer not found")
	}

	// 获取设备以更新配置文件
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device for config update: %v", err)
	}

	// 更新WireGuard配置文件
	if err := m.generateServerConfig(device); err != nil {
		return fmt.Errorf("failed to update server config: %v", err)
	}

	err = m.Helper.SyncConfig(device.Name)
	if err != nil {
		return fmt.Errorf("failed to sync config: %v", err)
	}

	return nil
}

func (m *Manager) DeletePeer(ctx context.Context, deviceName string, enterpriseID string, peerID primitive.ObjectID) error {
	// 获取要删除的peer信息
	peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	if err != nil {
		return fmt.Errorf("failed to get peer: %v", err)
	}

	// 从wg_peers集合中删除peer
	result, err := m.PeersColl.DeleteOne(ctx, bson.M{
		"_id":           peerID,
		"enterprise_id": enterpriseID,
	})

	if err != nil {
		return fmt.Errorf("failed to delete peer: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("peer not found")
	}

	// 获取设备信息以更新配置
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 释放IP地址
	if enterpriseIPAM, err := m.ipam.GetEnterpriseIPAM(enterpriseID); err == nil {
		peerIP := strings.TrimSuffix(peer.IP, "/32")
		enterpriseIPAM.ReleaseIP(peerIP)
	}

	// 更新WireGuard配置文件
	if err := m.generateServerConfig(device); err != nil {
		log.Printf("Warning: failed to update server config: %v", err)
	}

	err = m.Helper.SyncConfig(device.Name)
	if err != nil {
		return fmt.Errorf("failed to sync config: %v", err)
	}

	return nil
}

func (m *Manager) ListPeers(ctx context.Context, enterpriseID string) ([]Peer, error) {
	// 查询该企业的所有peers
	cursor, err := m.PeersColl.Find(ctx, bson.M{
		"enterprise_id": enterpriseID,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to query peers: %v", err)
	}
	defer cursor.Close(ctx)

	var peers []Peer
	if err := cursor.All(ctx, &peers); err != nil {
		return nil, fmt.Errorf("failed to decode peers: %v", err)
	}

	return peers, nil
}

// -------------------- Status --------------------
// GetInterfacesStatus 获取 WireGuard 接口状态
func (m *Manager) GetInterfacesStatus(ifaces []string) ([]DeviceStatus, error) {
	client, err := wgctrl.New()

	if err != nil {
		return nil, fmt.Errorf("failed to create wgctrl client: %v", err)
	}
	defer client.Close()

	deviceStatus := []DeviceStatus{}

	for _, iface := range ifaces {
		device, err := client.Device(iface)
		if err != nil {
			return nil, fmt.Errorf("failed to get interface status: %v", err)
		}

		//查询设备信息
		var deviceInfo Device
		err = m.DevicesColl.FindOne(context.Background(), bson.M{"device_name": iface}).Decode(&deviceInfo)
		if err != nil {
			return nil, err
		}
		deviceStatus = append(deviceStatus, DeviceStatus{
			Name:           device.Name,
			Status:         device.Type.String(),
			Port:           device.ListenPort,
			EnterpriseCode: deviceInfo.EnterpriseID,
		})
	}

	return deviceStatus, nil
}

// GetWGPeerStatus 获取 WireGuard 接口 Peer 状态
func (m *Manager) GetWGPeerStatus(iface string) ([]PeerStatusResponse, error) {
	client, err := wgctrl.New()
	if err != nil {
		return nil, fmt.Errorf("failed to create wgctrl client: %v", err)
	}
	defer client.Close()

	device, err := client.Device(iface)
	if err != nil {
		return nil, fmt.Errorf("failed to get interface status: %v", err)
	}

	deviceStatus := []PeerStatusResponse{}

	// 获取所有 peer 状态
	for _, wgPeer := range device.Peers {
		peerStatus := peer2rpc(wgPeer)
		deviceStatus = append(deviceStatus, peerStatus)
	}

	return deviceStatus, nil
}

// GetWgInterfaceBandwidth 计算指定 WireGuard 接口的总带宽（发送和接收的总字节数）
func (m *Manager) GetWgInterfaceBandwidth(iface string) (map[string]interface{}, error) {
	client, err := wgctrl.New()
	if err != nil {
		return nil, fmt.Errorf("failed to create wgctrl client: %v", err)
	}
	defer client.Close()

	// 获取设备信息
	var deviceInfo Device
	err = m.DevicesColl.FindOne(context.Background(), bson.M{"name": iface}).Decode(&deviceInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to get device info: %v", err)
	}

	// 初始化带宽统计变量
	bandwidth := map[string]interface{}{
		"device_name":   iface,
		"enterprise_id": deviceInfo.EnterpriseID,
		"totalReceived": 0,
		"totalSent":     0,
		"peersCount":    0,
		"status":        "stopped", // 默认为停止状态
	}

	// 尝试获取设备状态
	device, err := client.Device(iface)
	if err != nil {
		// 如果获取设备状态失败，说明接口未启动，返回默认值
		return bandwidth, nil
	}

	// 更新状态为运行中
	bandwidth["status"] = "running"
	bandwidth["peersCount"] = len(device.Peers)

	// 累加所有 peer 的发送和接收字节数
	var totalReceived, totalSent int
	for _, wgPeer := range device.Peers {
		totalReceived += int(wgPeer.ReceiveBytes)
		totalSent += int(wgPeer.TransmitBytes)
	}

	// 更新带宽信息
	bandwidth["totalReceived"] = totalReceived
	bandwidth["totalSent"] = totalSent

	return bandwidth, nil
}

func peer2rpc(wgPeer wgtypes.Peer) PeerStatusResponse {
	return PeerStatusResponse{
		PublicKey:     wgPeer.PublicKey.String(),
		LastHandshake: wgPeer.LastHandshakeTime,
		ReceiveBytes:  wgPeer.ReceiveBytes,
		TransmitBytes: wgPeer.TransmitBytes,
	}
}

// SetDeviceBandwidthLimit 设置设备的带宽限制
func (m *Manager) SetDeviceBandwidthLimit(ctx context.Context, deviceName string, enterpriseID string, limit uint32) error {
	// 获取设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 应用并保存带宽限制
	rateLimit := DefaultRateLimit(limit)
	if err := SaveAndApplyRateLimit(device.Name, enterpriseID, rateLimit); err != nil {
		return fmt.Errorf("failed to apply and save bandwidth limit: %v", err)
	}

	// 更新数据库中的带宽限制值
	filter := bson.M{"_id": device.ID}
	update := bson.M{"$set": bson.M{"bandwidth_limit": limit}}
	_, err = m.DevicesColl.UpdateOne(ctx, filter, update)
	if err != nil {
		// 尝试回滚带宽限制
		RemoveAndDeleteRateLimit(device.Name, enterpriseID)
		return fmt.Errorf("failed to update device bandwidth limit in database: %v", err)
	}

	return nil
}

// RemoveDeviceBandwidthLimit 移除设备的带宽限制
func (m *Manager) RemoveDeviceBandwidthLimit(ctx context.Context, deviceName string, enterpriseID string) error {
	// 获取设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 移除带宽限制并从数据库删除设置
	if err := RemoveAndDeleteRateLimit(device.Name, enterpriseID); err != nil {
		return fmt.Errorf("failed to remove and delete bandwidth limit: %v", err)
	}

	// 更新数据库中的带宽限制值
	filter := bson.M{"_id": device.ID}
	update := bson.M{"$unset": bson.M{"bandwidth_limit": ""}}
	_, err = m.DevicesColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update device bandwidth limit in database: %v", err)
	}

	return nil
}

// GetDeviceBandwidthLimit 获取设备的带宽限制
func (m *Manager) GetDeviceBandwidthLimit(ctx context.Context, deviceName string, enterpriseID string) (uint32, error) {
	// 获取设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return 0, fmt.Errorf("failed to get device: %v", err)
	}

	// 如果数据库中没有记录带宽限制，则尝试从系统或持久化存储获取
	if device.BandwidthLimit == 0 {
		limit, err, _ := GetCurrentRateLimit(device.Name, enterpriseID)
		if err != nil || limit == nil {
			// 如果系统中也没有设置带宽限制，则返回0
			return 0, nil
		}
		return limit.Rate, nil
	}

	return device.BandwidthLimit, nil
}

// ListAllRateLimits 列出所有接口的带宽限制
func (m *Manager) ListAllRateLimits(ctx context.Context) ([]RateLimitRecord, error) {
	return ListStoredRateLimits()
}

// EnsureAllRateLimitsApplied 确保所有存储的带宽限制被应用到接口
func (m *Manager) EnsureAllRateLimitsApplied(ctx context.Context) error {
	return RestoreAllRateLimits()
}

// GetServerPeerRoutes 获取服务器端Peer的路由配置
func (m *Manager) GetServerPeerRoutes(ctx context.Context, enterpriseID string, peerID primitive.ObjectID) ([]string, error) {
	// 获取peer信息
	peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get peer: %v", err)
	}

	// 基本路由总是包含peer自己的IP地址
	routes := []string{fmt.Sprintf("%s/32", peer.IP)}

	// 查询额外的路由信息
	cursor, err := db.MongoDB.Collection(PeerRoutesCollection).Find(ctx,
		bson.M{"peer_id": peerID, "enterprise_id": enterpriseID})

	// 如果没有找到额外路由，直接返回基本路由
	if err != nil && err != mongo.ErrNoDocuments {
		return routes, nil
	}

	if err == nil {
		defer cursor.Close(ctx)

		var route struct {
			Network string `bson:"network"`
		}

		for cursor.Next(ctx) {
			if err := cursor.Decode(&route); err == nil {
				routes = append(routes, route.Network)
			}
		}
	}

	return routes, nil
}

// UpdateServerPeerRoutes 更新服务器端Peer的路由配置
func (m *Manager) UpdateServerPeerRoutes(ctx context.Context, deviceName string, enterpriseID string,
	peerID primitive.ObjectID, routes []string) error {

	// 获取peer信息
	peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	if err != nil {
		return fmt.Errorf("failed to get peer: %v", err)
	}

	// 过滤掉peer自己的IP，这是默认已经包含的
	peerIPWithMask := fmt.Sprintf("%s/32", peer.IP)
	var additionalRoutes []string
	for _, route := range routes {
		if route != peerIPWithMask {
			additionalRoutes = append(additionalRoutes, route)
		}
	}

	// 清除现有的额外路由
	_, err = db.MongoDB.Collection(PeerRoutesCollection).DeleteMany(ctx,
		bson.M{"peer_id": peerID, "enterprise_id": enterpriseID})
	if err != nil {
		return fmt.Errorf("failed to clear existing routes: %v", err)
	}

	// 插入新的额外路由
	for _, route := range additionalRoutes {
		_, err = db.MongoDB.Collection(PeerRoutesCollection).InsertOne(ctx, bson.M{
			"peer_id":       peerID,
			"enterprise_id": enterpriseID,
			"network":       route,
			"created_at":    time.Now(),
		})
		if err != nil {
			return fmt.Errorf("failed to insert route %s: %v", route, err)
		}
	}

	// 获取设备以更新配置文件
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device for config update: %v", err)
	}

	// 更新WireGuard配置文件
	if err := m.generateServerConfig(device); err != nil {
		return fmt.Errorf("failed to update server config: %v", err)
	}

	// 同步配置到接口
	err = m.Helper.SyncConfig(device.Name)
	if err != nil {
		return fmt.Errorf("failed to sync config: %v", err)
	}

	return nil
}

// UpdatePeerAllowedIPs updates the AllowedIPs for a specific peer
func (m *Manager) UpdatePeerAllowedIPs(ctx context.Context, deviceName string, enterpriseID string, peerID primitive.ObjectID, allowedIPs []string) error {
	// 这个方法需要同时更新客户端和服务器端的配置

	// 1. 更新服务器端的路由配置
	err := m.UpdateServerPeerRoutes(ctx, deviceName, enterpriseID, peerID, allowedIPs)
	if err != nil {
		return fmt.Errorf("failed to update server routes: %v", err)
	}

	// 2. 如果需要获取peer信息可以取消注释
	// peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	// if err != nil {
	//	return fmt.Errorf("failed to get peer: %v", err)
	// }

	// 3. 更新客户端的AllowedIPs配置（保持不变，这是客户端需要路由的流量）
	// 注意：通常这里不需要修改，因为客户端的AllowedIPs通常是固定的网段
	// 但如果需要同步修改，可以取消下面的注释

	/*
		// 更新客户端配置
		update := bson.M{
			"$set": bson.M{
				"allowed_ips": allowedIPs,
			},
		}

		result, err := m.PeersColl.UpdateOne(
			ctx,
			bson.M{
				"_id":           peerID,
				"enterprise_id": enterpriseID,
			},
			update,
		)

		if err != nil {
			return fmt.Errorf("failed to update peer: %v", err)
		}

		if result.MatchedCount == 0 {
			return fmt.Errorf("peer not found")
		}
	*/

	return nil
}
