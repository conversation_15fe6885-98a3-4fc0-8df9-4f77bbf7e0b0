package wireguardmgr

import (
	"beacon/cloud/db"
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// RateLimitCollection is the MongoDB collection name for rate limit settings
	RateLimitCollection = "wg_rate_limits"
)

// RateLimitRecord represents a stored rate limit configuration in the database
type RateLimitRecord struct {
	InterfaceName string    `bson:"interface_name" json:"interface_name"`
	EnterpriseID  string    `bson:"enterprise_id" json:"enterprise_id"`
	Namespace     string    `bson:"namespace" json:"namespace"` // Network namespace
	Limit         RateLimit `bson:"limit" json:"limit"`
	CreatedAt     time.Time `bson:"created_at" json:"created_at"`
	UpdatedAt     time.Time `bson:"updated_at" json:"updated_at"`
}

// StoreRateLimit saves or updates a rate limit configuration in the database
func StoreRateLimit(iface string, enterpriseID string, limit RateLimit) error {
	return StoreRateLimitWithNamespace(iface, "", enterpriseID, limit)
}

// StoreRateLimitWithNamespace saves or updates a rate limit configuration in the database with namespace support
func StoreRateLimitWithNamespace(iface, namespace, enterpriseID string, limit RateLimit) error {
	ctx := context.Background()
	collection := db.MongoDB.Collection(RateLimitCollection)

	now := time.Now()
	record := RateLimitRecord{
		InterfaceName: iface,
		EnterpriseID:  enterpriseID,
		Namespace:     namespace,
		Limit:         limit,
		UpdatedAt:     now,
	}

	// Check if a record already exists for this interface
	var existingRecord RateLimitRecord
	err := collection.FindOne(ctx, bson.M{
		"interface_name": iface,
		"enterprise_id":  enterpriseID,
	}).Decode(&existingRecord)

	if err == mongo.ErrNoDocuments {
		// Record doesn't exist, create a new one
		record.CreatedAt = now
		_, err = collection.InsertOne(ctx, record)
		if err != nil {
			return fmt.Errorf("failed to insert rate limit record: %v", err)
		}
	} else if err != nil {
		// Database error
		return fmt.Errorf("failed to check for existing rate limit: %v", err)
	} else {
		// Record exists, update it
		_, err = collection.UpdateOne(
			ctx,
			bson.M{
				"interface_name": iface,
				"enterprise_id":  enterpriseID,
			},
			bson.M{"$set": bson.M{
				"namespace":  namespace,
				"limit":      limit,
				"updated_at": now,
			}},
		)
		if err != nil {
			return fmt.Errorf("failed to update rate limit record: %v", err)
		}
	}

	return nil
}

// DeleteRateLimit removes a rate limit configuration from the database
func DeleteRateLimit(iface string, enterpriseID string) error {
	ctx := context.Background()
	collection := db.MongoDB.Collection(RateLimitCollection)

	_, err := collection.DeleteOne(ctx, bson.M{
		"interface_name": iface,
		"enterprise_id":  enterpriseID,
	})

	if err != nil {
		return fmt.Errorf("failed to delete rate limit record: %v", err)
	}

	return nil
}

// GetStoredRateLimit retrieves a rate limit configuration from the database
func GetStoredRateLimit(iface string, enterpriseID string) (*RateLimit, error) {
	ctx := context.Background()
	collection := db.MongoDB.Collection(RateLimitCollection)

	var record RateLimitRecord
	err := collection.FindOne(ctx, bson.M{
		"interface_name": iface,
		"enterprise_id":  enterpriseID,
	}).Decode(&record)

	if err == mongo.ErrNoDocuments {
		return nil, nil // No rate limit configured
	} else if err != nil {
		return nil, fmt.Errorf("failed to get rate limit record: %v", err)
	}

	return &record.Limit, nil
}

// ListStoredRateLimits retrieves all rate limit configurations from the database
func ListStoredRateLimits() ([]RateLimitRecord, error) {
	ctx := context.Background()
	collection := db.MongoDB.Collection(RateLimitCollection)

	opts := options.Find().SetSort(bson.D{{Key: "enterprise_id", Value: 1}, {Key: "interface_name", Value: 1}})
	cursor, err := collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list rate limits: %v", err)
	}
	defer cursor.Close(ctx)

	var records []RateLimitRecord
	if err = cursor.All(ctx, &records); err != nil {
		return nil, fmt.Errorf("failed to decode rate limit records: %v", err)
	}

	return records, nil
}

// RestoreAllRateLimits applies all stored rate limits to their respective interfaces
// It is meant to be called at startup to recover rate limit configurations
func RestoreAllRateLimits() error {
	records, err := ListStoredRateLimits()
	if err != nil {
		return fmt.Errorf("failed to list rate limits for restoration: %v", err)
	}

	log.Printf("Restoring %d rate limits for WireGuard interfaces", len(records))

	var restoreErrors []string
	for _, record := range records {
		log.Printf("Restoring rate limit for interface %s in namespace %s: %d kbps",
			record.InterfaceName, record.Namespace, record.Limit.Rate)

		err := ApplyRateLimit(record.InterfaceName, record.Namespace, record.Limit)
		if err != nil {
			errMsg := fmt.Sprintf("failed to restore rate limit for %s in namespace %s: %v",
				record.InterfaceName, record.Namespace, err)
			restoreErrors = append(restoreErrors, errMsg)
			log.Printf("Warning: %s", errMsg)
		}
	}

	if len(restoreErrors) > 0 {
		return fmt.Errorf("encountered %d errors during rate limit restoration", len(restoreErrors))
	}

	return nil
}
