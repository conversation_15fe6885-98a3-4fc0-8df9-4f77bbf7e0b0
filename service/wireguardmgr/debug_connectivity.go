package wireguardmgr

import (
	"fmt"
	"log"
	"os/exec"
	"strings"
)

// DiagnoseNamespaceConnectivity diagnoses connectivity issues for a namespace
func (nm *NamespaceManager) DiagnoseNamespaceConnectivity(nsName string, wgPort int) error {
	log.Printf("=== Diagnosing connectivity for namespace: %s ===", nsName)

	// 1. Check if namespace exists
	namespaces, err := nm.ListNamespaces()
	if err != nil {
		return fmt.Errorf("failed to list namespaces: %v", err)
	}

	found := false
	for _, ns := range namespaces {
		if ns == nsName {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("namespace %s does not exist", nsName)
	}
	log.Printf("✓ Namespace %s exists", nsName)

	// 2. Check veth interfaces
	vethHost := nm.generateShortVethName(nsName, "h")
	vethNS := nm.generateShortVethName(nsName, "n")
	hostIP, nsIP := nm.generateVethIPs(nsName)

	// Check host side veth
	cmd := exec.Command("ip", "link", "show", vethHost)
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Host veth interface %s not found: %v", vethHost, err)
	} else {
		log.Printf("✓ Host veth interface %s exists", vethHost)
		if strings.Contains(string(output), "UP") {
			log.Printf("✓ Host veth interface %s is UP", vethHost)
		} else {
			log.Printf("✗ Host veth interface %s is DOWN", vethHost)
		}
	}

	// Check namespace side veth
	output, err = nm.ExecInNamespace(nsName, "ip", "link", "show", vethNS)
	if err != nil {
		log.Printf("✗ Namespace veth interface %s not found: %v", vethNS, err)
	} else {
		log.Printf("✓ Namespace veth interface %s exists", vethNS)
		if strings.Contains(string(output), "UP") {
			log.Printf("✓ Namespace veth interface %s is UP", vethNS)
		} else {
			log.Printf("✗ Namespace veth interface %s is DOWN", vethNS)
		}
	}

	// 3. Check IP addresses
	cmd = exec.Command("ip", "addr", "show", vethHost)
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Failed to get host veth IP: %v", err)
	} else {
		if strings.Contains(string(output), hostIP) {
			log.Printf("✓ Host veth has correct IP: %s", hostIP)
		} else {
			log.Printf("✗ Host veth missing IP %s", hostIP)
		}
	}

	output, err = nm.ExecInNamespace(nsName, "ip", "addr", "show", vethNS)
	if err != nil {
		log.Printf("✗ Failed to get namespace veth IP: %v", err)
	} else {
		if strings.Contains(string(output), nsIP) {
			log.Printf("✓ Namespace veth has correct IP: %s", nsIP)
		} else {
			log.Printf("✗ Namespace veth missing IP %s", nsIP)
		}
	}

	// 4. Check routing
	output, err = nm.ExecInNamespace(nsName, "ip", "route", "show")
	if err != nil {
		log.Printf("✗ Failed to get namespace routes: %v", err)
	} else {
		if strings.Contains(string(output), "default") {
			log.Printf("✓ Namespace has default route")
		} else {
			log.Printf("✗ Namespace missing default route")
		}
		log.Printf("Namespace routes:\n%s", string(output))
	}

	// 5. Check IP forwarding
	cmd = exec.Command("sysctl", "net.ipv4.ip_forward")
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Failed to check IP forwarding: %v", err)
	} else {
		if strings.Contains(string(output), "= 1") {
			log.Printf("✓ IP forwarding is enabled")
		} else {
			log.Printf("✗ IP forwarding is disabled")
		}
	}

	// 6. Check iptables rules
	log.Printf("=== Checking iptables rules ===")

	// Check DNAT rule
	cmd = exec.Command("iptables", "-t", "nat", "-L", "PREROUTING", "-n", "-v")
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Failed to check DNAT rules: %v", err)
	} else {
		dnatRule := fmt.Sprintf("dpt:%d to:%s:%d", wgPort, nsIP, wgPort)
		if strings.Contains(string(output), dnatRule) {
			log.Printf("✓ DNAT rule found for port %d -> %s:%d", wgPort, nsIP, wgPort)
		} else {
			log.Printf("✗ DNAT rule missing for port %d -> %s:%d", wgPort, nsIP, wgPort)
			log.Printf("PREROUTING rules:\n%s", string(output))
		}
	}

	// Check SNAT rule
	cmd = exec.Command("iptables", "-t", "nat", "-L", "POSTROUTING", "-n", "-v")
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Failed to check SNAT rules: %v", err)
	} else {
		if strings.Contains(string(output), "MASQUERADE") {
			log.Printf("✓ MASQUERADE rule found")
		} else {
			log.Printf("✗ MASQUERADE rule missing")
		}
	}

	// Check FORWARD rules
	cmd = exec.Command("iptables", "-L", "FORWARD", "-n", "-v")
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Failed to check FORWARD rules: %v", err)
	} else {
		if strings.Contains(string(output), vethHost) {
			log.Printf("✓ FORWARD rules found for %s", vethHost)
		} else {
			log.Printf("✗ FORWARD rules missing for %s", vethHost)
		}
	}

	// 7. Test connectivity
	log.Printf("=== Testing connectivity ===")

	// Test ping from namespace to host
	output, err = nm.ExecInNamespace(nsName, "ping", "-c", "1", "-W", "2", hostIP)
	if err != nil {
		log.Printf("✗ Cannot ping host from namespace: %v", err)
	} else {
		log.Printf("✓ Can ping host from namespace")
	}

	// Test ping from host to namespace
	cmd = exec.Command("ping", "-c", "1", "-W", "2", nsIP)
	output, err = cmd.CombinedOutput()
	if err != nil {
		log.Printf("✗ Cannot ping namespace from host: %v", err)
	} else {
		log.Printf("✓ Can ping namespace from host")
	}

	log.Printf("=== Diagnosis complete ===")
	return nil
}

// GetMainNetworkInterface returns the main network interface
func (nm *NamespaceManager) GetMainNetworkInterface() (string, error) {
	return nm.detectMainInterface()
}

// ListActiveNamespaces lists all active WireGuard namespaces
func (nm *NamespaceManager) ListActiveNamespaces() ([]string, error) {
	namespaces, err := nm.ListNamespaces()
	if err != nil {
		return nil, err
	}

	var wgNamespaces []string
	for _, ns := range namespaces {
		if strings.HasPrefix(ns, "wg-") {
			wgNamespaces = append(wgNamespaces, ns)
		}
	}

	return wgNamespaces, nil
}
