package mqttserver

import (
	"beacon/cloud/db"
	"context"
	"log"
	"time"

	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/listeners"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// 全局变量
var (
	MqttServer *mqtt.Server // MQTT 服务器实例
)

// NewMQTTServer 创建一个新的 MQTT 服务器实例
func NewMQTTServer() {
	// 初始化全局变量
	MqttServer = mqtt.New(&mqtt.Options{
		InlineClient: true,
	})

	// 确保topics集合有索引
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := db.MongoDB.Collection("mqtt_topics").Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys:    bson.D{{Key: "topic", Value: 1}},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.Printf("Error creating index on mqtt_topics: %v", err)
	}
}

// Start 启动 MQTT 服务器
func Start() error {
	if MqttServer == nil {
		log.Fatal("MQTT server or DB is not initialized. Call NewMQTTServer first.")
	}

	// 配置 TCP 监听器
	tcpConfig := listeners.Config{
		ID:      "tcp1",
		Address: ":1883",
	}

	// 添加 TCP 监听器
	tcp := listeners.NewTCP(tcpConfig)
	if err := MqttServer.AddListener(tcp); err != nil {
		log.Fatal("Failed to add TCP listener:", err)
		return err
	}

	// 添加 自定义 钩子
	dbHook := &CustomHook{}
	if err := MqttServer.AddHook(dbHook, nil); err != nil {
		log.Fatal("Failed to add CustomHook:", err)
		return err
	}
	log.Println("CustomHook successfully added")

	// 启动服务器
	go func() {
		if err := MqttServer.Serve(); err != nil {
			log.Fatal("MQTT server failed to start:", err)
		}
	}()

	log.Printf("MQTT server started on :1883")
	return nil
}

// Stop 停止 MQTT 服务器
func Stop() {
	if MqttServer == nil {
		log.Fatal("MQTT server is not initialized. Call NewMQTTServer first.")
	}
	MqttServer.Close()
	log.Println("MQTT server stopped")
}

// Publish 发布消息
func Publish(topic string, payload string) error {
	return MqttServer.Publish(topic, []byte(payload), false, 0)
}
