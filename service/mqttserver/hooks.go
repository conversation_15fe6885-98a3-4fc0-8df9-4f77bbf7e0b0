package mqttserver

import (
	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/logger"
	"beacon/cloud/service/notification"
	"bytes"
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/packets"
	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/crypto/bcrypt"
)

var (
	silenceCache = cache.New(time.Hour, 10*time.Minute) // 静默期缓存，1小时过期
	alertMutex   sync.Mutex                             // 用于报警插入的互斥锁
)

const (
	TAG = "MqttServer"
)

// CustomHook : 一个自定义钩子，用于将消息保存到 DB
type CustomHook struct {
	mqtt.HookBase
}

// ID 返回钩子的唯一标识符
func (h *CustomHook) ID() string {
	return "mongodb-hook"
}

// Init 初始化钩子
func (h *CustomHook) Init(config any) error {
	h.Log.Info("CustomHook initialized")
	return nil
}

// Provides 返回钩子提供的功能
func (h *CustomHook) Provides(b byte) bool {
	return bytes.Contains([]byte{
		mqtt.OnPublish,
		mqtt.OnSubscribed,
		mqtt.OnUnsubscribed,
		mqtt.OnSubscribe,
		mqtt.OnConnectAuthenticate,
		mqtt.OnACLCheck,
	}, []byte{b})
}

// OnConnectAuthenticate 在客户端连接时进行认证，仅认证用户名密码;
func (h *CustomHook) OnConnectAuthenticate(cl *mqtt.Client, pk packets.Packet) bool {
	// 仅处理 CONNECT 包
	if pk.FixedHeader.Type != packets.Connect {
		return false
	}

	username := string(pk.Connect.Username)
	password := string(pk.Connect.Password)

	if username == "" || password == "" {
		h.Log.Info("Empty username or password")
		return false
	}

	// 检查用户缓存
	cacheKey := fmt.Sprintf("user:%s:password", username)
	cachedPassword, err := db.Redis.Get(context.Background(), cacheKey).Result()

	if err == nil && cachedPassword != "" {
		// 如果密码匹配缓存的结果，直接认证
		if bcrypt.CompareHashAndPassword([]byte(cachedPassword), []byte(password)) == nil {
			cl.Properties.Username = []byte(username)
			fmt.Printf("[MqttServer] ACL cache hit for %s: %v", username, true)
			return true
		}
	}

	// 查询用户表
	collection := db.MongoDB.Collection("users")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var user models.User
	err = collection.FindOne(ctx, bson.M{"username": username}).Decode(&user)
	if err != nil {
		h.Log.Info("User not found: " + username)
		return false
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		h.Log.Info("Invalid password for user: " + username)
		return false
	}

	// 设置密码到 Redis 缓存
	db.Redis.Set(context.Background(), cacheKey, user.Password, time.Hour)

	cl.Properties.Username = []byte(user.Username)
	return true
}

// checkPermissionFromCache 检查Redis缓存中的权限
func checkPermissionFromCache(cacheKey string) (bool, bool) {
	hasPermission, err := db.Redis.Get(context.Background(), cacheKey).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Logger(logger.LogLevelError, TAG, "Redis error when checking ACL: %v", err)
		}
		return false, false
	}
	logger.Logger(logger.LogLevelDebug, TAG, "ACL cache hit: %v", hasPermission)
	return true, hasPermission == "1"
}

// verifyDeviceAccess 验证用户对设备的访问权限
func verifyDeviceAccess(ctx context.Context, username, topic string) (*models.User, *models.Topic, *models.Device, error) {
	// 1. 获取用户信息
	var user models.User
	if err := db.MongoDB.Collection("users").FindOne(ctx, bson.M{
		"username": username,
	}).Decode(&user); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to find user: %v", err)
	}

	// 2. 获取主题信息
	var topicDoc models.Topic
	if err := db.MongoDB.Collection("mqtt_topics").FindOne(ctx, bson.M{
		"topic": topic,
	}).Decode(&topicDoc); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to find topic: %v", err)
	}

	// 3. 获取设备信息
	var device models.Device
	if err := db.MongoDB.Collection("devices").FindOne(ctx, bson.M{
		"device_sn": topicDoc.DeviceSN,
	}).Decode(&device); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to find device: %v", err)
	}

	return &user, &topicDoc, &device, nil
}

// OnACLCheck 检查访问控制权限
func (h *CustomHook) OnACLCheck(cl *mqtt.Client, topic string, write bool) bool {
	username := string(cl.Properties.Username)
	cacheKey := fmt.Sprintf("acl:%s:%s:%v", username, topic, write)

	// 检查缓存
	if found, result := checkPermissionFromCache(cacheKey); found {
		return result
	}

	// 验证访问权限
	ctx := context.Background()
	user, _, device, err := verifyDeviceAccess(ctx, username, topic)
	if err != nil {
		logger.Logger(logger.LogLevelError, TAG, "Access verification failed: %v", err)
		cacheResult(cacheKey, false)
		return false
	}

	// 检查企业权限和操作者权限
	if user.EnterpriseID != device.EnterpriseID {
		logger.Logger(logger.LogLevelError, TAG, "Enterprise mismatch for user %s", username)
		cacheResult(cacheKey, false)
		return false
	}

	isOperator := false
	for _, operator := range device.Operators {
		if operator == username {
			isOperator = true
			break
		}
	}

	if !isOperator {
		logger.Logger(logger.LogLevelError, TAG, "User %s is not an operator for device %s", username, device.DeviceSN)
		cacheResult(cacheKey, false)
		return false
	}

	cacheResult(cacheKey, true)
	logger.Logger(logger.LogLevelDebug, TAG, "ACL cache miss, permission granted for %s", username)
	return true
}

// cacheResult 缓存权限检查结果
func cacheResult(key string, allowed bool) {
	result := map[bool]string{true: "1", false: "0"}[allowed]
	db.Redis.Set(context.Background(), key, result, time.Hour)
}

// validatePayload 验证消息负载是否符合指定的数据类型
func validatePayload(payload string, dataType string) error {
	switch dataType {
	case "number":
		if _, err := strconv.ParseFloat(strings.TrimSpace(payload), 64); err != nil {
			return fmt.Errorf("invalid number format: %s", payload)
		}
	case "boolean":
		payload = strings.ToLower(strings.TrimSpace(payload))
		if payload != "true" && payload != "false" {
			return fmt.Errorf("invalid boolean format: %s", payload)
		}
	case "string":
		return nil
	default:
		return fmt.Errorf("unsupported data type: %s", dataType)
	}
	return nil
}

// AlertWorkerPool 预警工作池
type AlertWorkerPool struct {
	taskQueue chan models.AlertTask
	workers   int
}

var alertPool *AlertWorkerPool

// NewAlertWorkerPool 创建新的预警工作池
func NewAlertWorkerPool(workers int, queueSize int) *AlertWorkerPool {
	pool := &AlertWorkerPool{
		taskQueue: make(chan models.AlertTask, queueSize),
		workers:   workers,
	}
	pool.Start()
	return pool
}

// Start 启动工作池
func (p *AlertWorkerPool) Start() {
	for i := 0; i < p.workers; i++ {
		go p.worker()
	}
}

// worker 工作协程
func (p *AlertWorkerPool) worker() {
	for task := range p.taskQueue {
		// 检查静默期
		if _, found := silenceCache.Get(task.Rule.Topic); found {
			log.Printf("[MqttServer] Topic %s is in silence period, skipping alert", task.Rule.Topic)
			continue
		}

		// 使用通知服务工厂获取适合的通知服务
		notificationService := notification.NotificationFactory(task.Rule.Mode)

		// 转换任务类型为通知服务需要的类型
		notificationTask := models.AlertTask{
			Payload:   task.Payload,
			Rule:      task.Rule,
			Value:     task.Value,
			Retries:   task.Retries,
			IsTrigger: task.IsTrigger,
		}

		// Get device info with proper error handling
		deviceName := task.Rule.Topic // default to topic if device not found
		sn := strings.Split(task.Rule.Topic, "/")[0]
		device, err := models.FindDeviceBySN(sn, db.MongoDB.Collection("devices"))
		if err != nil {
			log.Printf("[MqttServer] Warning: Could not find device for topic %s: %v", task.Rule.Topic, err)
		} else if device != nil {
			deviceName = device.Name
		}

		err = notificationService.Send(notificationTask, deviceName)
		errmsg := "successfully"
		// 处理结果
		if err == nil {
			// 设置静默期
			silenceCache.Set(task.Rule.Topic, struct{}{}, cache.DefaultExpiration)
		} else if task.Retries < 3 { // 最大重试3次
			// 准备重试
			task.Retries++
			time.AfterFunc(2*time.Minute, func() {
				if err := alertPool.Submit(task); err != nil {
					log.Printf("[MqttServer] Failed to resubmit alert task: %v", err)
				}
			})
		} else {
			errmsg = err.Error()
		}
		alertMutex.Lock()
		InsertAlarm(
			task.Rule.Topic,
			task.Payload,
			task.Rule.Description,
			task.Rule.Mode,
			task.Rule.Contact,
			errmsg,
			task.Rule.EnterpriseID,
		)
		alertMutex.Unlock()
	}
}

// Submit 提交预警任务
func (p *AlertWorkerPool) Submit(task models.AlertTask) error {
	select {
	case p.taskQueue <- task:
		return nil
	default:
		return fmt.Errorf("alert queue is full")
	}
}

// 在 init 函数或服务启动时初始化工作池
func init() {
	// 创建一个有 5 个工作协程、队列大小为 100 的工作池
	alertPool = NewAlertWorkerPool(5, 100)
}

// OnPublish 在消息发布时触发，将消息保存到 MongoDB
func (h *CustomHook) OnPublish(cl *mqtt.Client, pk packets.Packet) (packets.Packet, error) {
	var isAlarm bool = false
	log.Printf("[MqttServer] Received message on topic %s: %s", pk.TopicName, string(pk.Payload))

	// 获取主题信息和数据类型
	collection := db.MongoDB.Collection("mqtt_topics")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var topic models.Topic
	err := collection.FindOne(ctx, bson.M{"topic": pk.TopicName}).Decode(&topic)
	if err != nil {
		log.Printf("[MqttServer] Error finding topic: %v", err)
		return pk, err
	}

	// 验证消息负载是否符合数据类型
	if err := validatePayload(string(pk.Payload), topic.DataType); err != nil {
		log.Printf("[MqttServer] Payload validation failed for topic %s", pk.TopicName)
		return pk, err
	}

	// 更新主题的最后更新时间
	_, err = collection.UpdateOne(
		ctx,
		bson.M{"topic": pk.TopicName},
		bson.M{
			"$set": bson.M{
				"last_updated": time.Now(),
			},
		},
	)
	if err != nil {
		log.Printf("[MqttServer] Error updating topics collection: %v", err)
	}

	rule, err := GetThresholdRule(topic.Topic)
	if err != nil {
		log.Printf("[MqttServer] Error finding threshold rule for topic %s: %v", topic.Topic, err)
	} else if rule.IsEnabled && rule.Expression != "" {
		value, err := strconv.ParseFloat(strings.TrimSpace(string(pk.Payload)), 64)
		if err != nil {
			log.Printf("[MqttServer] Invalid number format for topic %s: %s", topic.Topic, string(pk.Payload))
		} else {
			isTrigger, err := evaluateExpression(rule.Expression, value)
			if err != nil {
				log.Printf("[MqttServer] Error evaluating expression for topic %s: %v", topic.Topic, err)
			} else if isTrigger {
				isAlarm = true
				// Validate that rule has required fields
				if rule.Mode == "" {
					log.Printf("[MqttServer] Warning: Threshold rule for topic %s has no notification mode defined", topic.Topic)
				} else if rule.Contact == "" {
					log.Printf("[MqttServer] Warning: Threshold rule for topic %s has no contact information defined", topic.Topic)
				} else {
					// 使用工作池处理预警，包含重试机制
					task := models.AlertTask{
						Payload:   string(pk.Payload),
						Rule:      rule,
						Value:     value,
						Retries:   0,
						IsTrigger: true,
					}

					if alertPool == nil {
						log.Printf("[MqttServer] Error: Alert worker pool not initialized")
					} else if err := alertPool.Submit(task); err != nil {
						log.Printf("[MqttServer] Failed to submit alert task: %v", err)
					} else {
						log.Printf("[MqttServer] Successfully submitted alert task for topic %s", topic.Topic)
					}
				}
			}
		}
	}

	// 保存消息
	err = InsertMessage(
		sanitizeTableName(topic.Topic),
		topic.EnterpriseID,
		cl.ID,
		pk.TopicName,
		string(pk.Payload),
		pk.FixedHeader.Qos,
		isAlarm,
	)

	if err != nil {
		log.Printf("[MqttServer] Error saving message to Tdengine: %v", err)
		return pk, err
	}

	return pk, nil
}

// OnSubscribed 处理订阅事件
func (h *CustomHook) OnSubscribed(cl *mqtt.Client, pk packets.Packet, reasonCodes []byte) {
	// 遍历所有请求订阅的 Topic 过滤器
	for i, filter := range pk.Filters {
		// 检查reasonCodes，如果不是成功的状态码，则跳过
		if i < len(reasonCodes) && reasonCodes[i] >= 0x80 {
			log.Printf("[MqttServer] Client %s subscription to topic %s was rejected with code %d", cl.ID, filter.Filter, reasonCodes[i])
			continue
		}

		log.Printf("[MqttServer] Client %s subscribed to topic: %s", cl.ID, filter.Filter)

		// 更新主题的订阅者数量
		ctx := context.Background()
		collection := db.MongoDB.Collection("mqtt_topics")
		_, err := collection.UpdateOne(
			ctx,
			bson.M{"topic": filter.Filter},
			bson.M{
				"$set": bson.M{
					"last_updated": time.Now(),
				},
				"$inc": bson.M{
					"subscribers_count": 1,
				},
			},
		)
		if err != nil {
			log.Printf("[MqttServer] Error updating topic on subscribe: %v", err)
		}
	}
}

// OnUnsubscribed 处理取消订阅事件
func (h *CustomHook) OnUnsubscribed(cl *mqtt.Client, pk packets.Packet) {
	// 遍历取消订阅的过滤器（主题）
	for _, filter := range pk.Filters {
		log.Printf("[MqttServer] Client %s unsubscribed from topic: %s", cl.ID, filter.Filter)

		ctx := context.Background()
		topicsCollection := db.MongoDB.Collection("mqtt_topics")

		// 更新主题记录，减少订阅者数量
		_, err := topicsCollection.UpdateOne(
			ctx,
			bson.M{"topic": filter.Filter},
			bson.M{
				"$inc": bson.M{
					"subscribers_count": -1,
				},
				"$set": bson.M{
					"last_updated": time.Now(),
				},
			},
		)
		if err != nil {
			log.Printf("[MqttServer] Error updating subscribers count: %v", err)
		}
	}
}
