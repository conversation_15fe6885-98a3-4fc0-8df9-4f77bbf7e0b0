package notification

import (
	"beacon/cloud/config"
	"beacon/cloud/models"
	"beacon/cloud/service/logger"
	"beacon/cloud/service/wechat"
	"fmt"
	"regexp"
	"time"
)

// WeChat notification service implements NotificationService interface
type WechatNotificationService struct {
	MaxRetries int
}

// NewWechatNotificationService creates a new instance of WeChat notification service
func NewWechatNotificationService() *WechatNotificationService {
	return &WechatNotificationService{
		MaxRetries: 3,
	}
}

func truncateString(s string, maxLen int) string {
	runes := []rune(s)
	if len(runes) > maxLen {
		return string(runes[:maxLen])
	}
	return s
}

// Send implements NotificationService interface
func (s *WechatNotificationService) Send(task models.AlertTask, deviceName string) error {
	// Extract OpenID from the contact field
	// Format is typically "nickname(openid)"
	openID := extractOpenID(task.Rule.Contact)
	if openID == "" {
		return fmt.Errorf("invalid WeChat contact format: %s", task.Rule.Contact)
	}

	// Create template data for WeChat message
	templateData := map[string]any{
		"touser":      openID,
		"template_id": config.AppConfig.Wechat.TemplateID,
		"data": map[string]any{
			"time4": map[string]any{
				"value": time.Now().Format("2006-01-02 15:04:05"),
			},
			"thing2": map[string]any{
				"value": deviceName,
			},
			"character_string11": map[string]any{
				"value": task.Rule.Topic,
			},
			"character_string14": map[string]any{
				"value": fmt.Sprintf("%.3f", task.Value),
			},
			"thing5": map[string]any{
				"value": truncateString(task.Rule.Description, 20),
			},
		},
	}

	// Send WeChat notification with retries
	var err error
	for attempt := 0; attempt < s.MaxRetries; attempt++ {
		err = wechat.SendWxTemplateMessage(config.AppConfig.Wechat.TemplateID, openID, templateData)
		if err == nil {
			logger.Logger(logger.LogLevelInfo, "MQTT", "WeChat notification sent successfully to %s", task.Rule.Contact)
			return nil
		}

		logger.Logger(logger.LogLevelError, "MQTT", "Failed to send WeChat notification (attempt %d/%d): %v",
			attempt+1, s.MaxRetries, err)

		// Wait before retrying (exponential backoff)
		if attempt < s.MaxRetries-1 {
			time.Sleep(time.Duration(1<<attempt) * time.Second)
		}
	}

	return fmt.Errorf("failed to send WeChat notification after %d attempts: %v", s.MaxRetries, err)
}

// Helper function to extract OpenID from contact string
func extractOpenID(contact string) string {
	// Expected format: "nickname(openid)"
	re := regexp.MustCompile(`\((.*?)\)`)
	matches := re.FindStringSubmatch(contact)
	if len(matches) >= 2 {
		return matches[1]
	}
	return ""
}

// Helper function to extract device from topic
// func extractDeviceFromTopic(topic string) string {
// 	parts := strings.SplitN(topic, "/", 2)
// 	if len(parts) > 0 {
// 		return parts[0]
// 	}
// 	return topic
// }
