package notification

import (
	"beacon/cloud/models"
)

// LogNotificationService 实现日志通知服务（用作备份通知方式或默认实现）
type LogNotificationService struct{}

// NewLogNotificationService 创建一个新的日志通知服务实例
func NewLogNotificationService() *LogNotificationService {
	return &LogNotificationService{}
}

// Send 将通知写入日志
func (s *LogNotificationService) Send(task models.AlertTask, deviceName string) error {
	// 构建状态消息
	// statusMessage := "did not trigger"
	// if task.IsTrigger {
	// 	statusMessage = "triggered"
	// }

	// 记录详细日志
	// logMessage := fmt.Sprintf(
	// 	"[ALERT] Topic: %s, Value: %f %s Expression: %s, Mode: %s, Contact: %s",
	// 	task.Rule.Topic,
	// 	task.Value,
	// 	statusMessage,
	// 	task.Rule.Expression,
	// 	task.Rule.Mode,
	// 	task.Rule.Contact,
	// )
	// log.Printf("%s", logMessage)

	// 日志通知总是视为成功
	return nil
}
