package exporter

import (
	"fmt"
	"net"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

//---------------------------------------------devider--------------------------------------------------

// sysinfo helpers
// 获取 CPU 使用率
func GetCPUUsage() (float64, error) {
	// 通过读取 /proc/stat 文件获取 CPU 使用情况
	data, err := os.ReadFile("/proc/stat")
	if err != nil {
		return 0, err
	}

	lines := strings.Split(string(data), "\n")
	fields := strings.Fields(lines[0])
	if len(fields) < 5 {
		return 0, fmt.Errorf("invalid /proc/stat format")
	}

	// 计算 CPU 使用率
	totalTime := 0
	idleTime := 0
	for i, val := range fields[1:] {
		valInt, _ := strconv.Atoi(val)
		totalTime += valInt
		if i == 3 { // idle
			idleTime = valInt
		}
	}
	usage := float64(totalTime-idleTime) / float64(totalTime) * 100
	return usage, nil
}

// 获取内存信息
func GetMemoryUsage() (uint64, uint64, uint64, error) {
	// 读取 /proc/meminfo 文件
	data, err := os.ReadFile("/proc/meminfo")
	if err != nil {
		return 0, 0, 0, err
	}

	var total, free, available uint64
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "MemTotal:") {
			fmt.Sscanf(line, "MemTotal: %d kB", &total)
		} else if strings.HasPrefix(line, "MemFree:") {
			fmt.Sscanf(line, "MemFree: %d kB", &free)
		} else if strings.HasPrefix(line, "MemAvailable:") {
			fmt.Sscanf(line, "MemAvailable: %d kB", &available)
		}
	}

	return total, free, available, nil
}

// 获取磁盘信息
func GetDiskUsage() (uint64, uint64, error) {
	// 使用 df 命令获取磁盘使用情况
	cmd := exec.Command("df", "/")
	output, err := cmd.Output()
	if err != nil {
		return 0, 0, err
	}

	// 解析 df 命令输出
	lines := strings.Split(string(output), "\n")
	fields := strings.Fields(lines[1])
	if len(fields) < 5 {
		return 0, 0, fmt.Errorf("invalid df output format")
	}

	// 解析磁盘信息
	total, _ := strconv.ParseUint(fields[1], 10, 64)
	used, _ := strconv.ParseUint(fields[2], 10, 64)

	return total, used, nil
}

// 获取网络吞吐量
func GetNetworkTransmit(Iface string) (uint64, uint64, error) {
	// 读取 /proc/net/dev 文件
	data, err := os.ReadFile("/proc/net/dev")
	if err != nil {
		return 0, 0, err
	}

	var bytesSent, bytesReceived uint64
	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if strings.Contains(line, Iface) {
			fields := strings.Fields(line)
			if len(fields) >= 10 {
				bytesReceived, _ = strconv.ParseUint(fields[1], 10, 64)
				bytesSent, _ = strconv.ParseUint(fields[9], 10, 64)
			}
		}
	}

	return bytesSent, bytesReceived, nil
}

// 获取系统基本信息
func GetBasicInfo() (string, string, string, error) {
	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		return "", "", "", err
	}

	// 获取内核版本
	kernel, err := os.ReadFile("/proc/version")
	if err != nil {
		return "", "", "", err
	}
	kernelVersion := strings.Split(string(kernel), " ")[2]

	// 获取系统运行时间
	uptime, err := os.ReadFile("/proc/uptime")
	if err != nil {
		return "", "", "", err
	}
	uptimeSeconds, _ := strconv.ParseFloat(strings.Split(string(uptime), " ")[0], 64)
	uptimeDuration := time.Duration(uptimeSeconds) * time.Second

	return hostname, kernelVersion, formatUptime(uptimeDuration), nil
}

func formatUptime(d time.Duration) string {
	days := int(d.Hours() / 24)
	hours := int(d.Hours()) % 24
	minutes := int(d.Minutes()) % 60

	return fmt.Sprintf("%dd %dh %dm", days, hours, minutes)
}

// getNetworkInterfaces returns a list of active network interfaces
func GetNetworkInterfaces() ([]string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, err
	}

	var activeInterfaces []string
	for _, iface := range interfaces {
		// Skip loopback, non-up interfaces and interfaces without addresses
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		// Only include interfaces with IP addresses
		if len(addrs) > 0 {
			activeInterfaces = append(activeInterfaces, iface.Name)
		}
	}

	return activeInterfaces, nil
}
