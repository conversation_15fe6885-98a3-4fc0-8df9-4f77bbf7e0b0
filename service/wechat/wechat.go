package wechat

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"beacon/cloud/service/logger"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/skip2/go-qrcode"
)

type CheckSignatureRequest struct {
	Signature string `form:"signature"`
	Timestamp string `form:"timestamp"`
	Nonce     string `form:"nonce"`
	Echostr   string `form:"echostr"`
}

type WeChat struct {
	AppId       string `json:"app_id"`
	AppSecret   string `json:"app_secret"`
	AccessToken string `json:"access_token"`
}

type SnsOauth2 struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	Openid       string `json:"openid"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
}

type AccessTokenErrorResponse struct {
	ErrMsg  string `json:"err_msg"`
	ErrCode string `json:"err_code"`
}

const (
	code2sessionURL    = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code"
	oauth2URL          = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect"
	accessTokenURL     = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s"
	sendTemplateURL    = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s"
	authAccessTokenURL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code"
	userInfoURL        = "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN"
)

func GenerateQRCode(sceneID, redirect_uri string) ([]byte, error) {
	// 构造微信授权URL
	authURL := fmt.Sprintf(oauth2URL,
		config.AppConfig.Wechat.AppID,
		url.PathEscape(redirect_uri),
		sceneID)
	var png []byte
	png, err := qrcode.Encode(authURL, qrcode.Medium, 256)
	if err != nil {
		return nil, err
	}
	return png, nil
}

type WechatTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

func GetWechatAccessToken() string {
	// Try to get token from Redis first
	accessToken, err := db.Redis.Get(db.Ctx, "wechat:access_token").Result()
	if err == nil && accessToken != "" {
		// Token exists in cache, check if still valid
		logger.Logger(logger.LogLevelInfo, "WeChat", "Using cached access token")
		return accessToken
	}

	// Token not found in cache or expired, request a new one
	appid := config.AppConfig.Wechat.AppID
	secret := config.AppConfig.Wechat.Secret
	reqUrl := fmt.Sprintf(accessTokenURL, appid, secret)
	resp, err := http.Get(reqUrl)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error requesting access token: %v", err)
		return ""
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error reading response body: %v", err)
		return ""
	}

	var result WechatTokenResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error parsing response: %v", err)
		return ""
	}

	// Cache the token in Redis
	// Set expiration time with 10% buffer to ensure we refresh before actual expiry
	expirySeconds := result.ExpiresIn * 90 / 100 // 90% of the expiration time
	err = db.Redis.Set(db.Ctx, "wechat:access_token", result.AccessToken, time.Duration(expirySeconds)*time.Second).Err()
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Failed to cache access token:%v", err)
	} else {
		// Store expiry time for debugging/monitoring
		db.Redis.Set(db.Ctx, "wechat:token_expires_at", strconv.FormatInt(time.Now().Unix()+int64(expirySeconds), 10), time.Duration(expirySeconds)*time.Second)
		logger.Logger(logger.LogLevelInfo, "WeChat", "Cached new access token, expires in %d seconds", expirySeconds)
	}

	return result.AccessToken
}

func SendWxTemplateMessage(templateId string, openid string, data map[string]interface{}) error {
	// https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN
	accessToken := GetWechatAccessToken()
	reqUrl := fmt.Sprintf(sendTemplateURL, accessToken)
	reqbody, err := json.Marshal(data)
	if err != nil {
		return err
	}
	req, err := http.NewRequest("POST", reqUrl, bytes.NewBuffer(reqbody))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// fmt.Println(string(body))

	// Define a structure for the WeChat API response
	var response struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}

	// Unmarshal the response
	if err := json.Unmarshal(body, &response); err != nil {
		return fmt.Errorf("failed to unmarshal WeChat response: %w", err)
	}

	// Check if the API returned an error
	if response.ErrCode != 0 {
		return fmt.Errorf("wechat API error: errcode=%d, errmsg=%s", response.ErrCode, response.ErrMsg)
	}

	return nil
}

func GetWxOpenIdFromOauth2(code string) (*SnsOauth2, error) {
	requestLine := fmt.Sprintf(authAccessTokenURL, config.AppConfig.Wechat.AppID, config.AppConfig.Wechat.Secret, code)

	resp, err := http.Get(requestLine)
	if err != nil || resp.StatusCode != http.StatusOK {
		logger.Logger(logger.LogLevelError, "WeChat", "Error requesting access token: %v", err)
		return nil, fmt.Errorf("failed to get access token: %v", err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error reading response body: %v", err)
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}
	if bytes.Contains(body, []byte("errcode")) {
		ater := AccessTokenErrorResponse{}
		err = json.Unmarshal(body, &ater)
		if err != nil {
			logger.Logger(logger.LogLevelError, "WeChat", "Error unmarshalling access token error response: %v", err)
			return nil, fmt.Errorf("failed to unmarshal access token error response: %v", err)
		}
		return nil, fmt.Errorf("%s", ater.ErrMsg)
	} else {
		atr := SnsOauth2{}
		err = json.Unmarshal(body, &atr)
		if err != nil {
			logger.Logger(logger.LogLevelError, "WeChat", "Error unmarshalling access token response: %v", err)
			return nil, fmt.Errorf("failed to unmarshal access token response: %v", err)
		}
		return &atr, nil
	}
}

func GetWxUserInfoFromOpenId(code string) (*WxUserInfo, error) {
	auth, err := GetWxOpenIdFromOauth2(code)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error getting openid: %v", err)
		return nil, fmt.Errorf("failed to get openid: %v", err)
	}
	requestLine := fmt.Sprintf(userInfoURL, auth.AccessToken, auth.Openid)
	resp, err := http.Get(requestLine)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error requesting user info: %v", err)
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error reading response body: %v", err)
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}
	if bytes.Contains(body, []byte("errcode")) {
		logger.Logger(logger.LogLevelError, "WeChat", "Error requesting user info: %v", err)
		return nil, fmt.Errorf("failed to get user info: %v", err)
	}
	userInfo := WxUserInfo{}
	err = json.Unmarshal(body, &userInfo)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error unmarshalling user info: %v", err)
		return nil, fmt.Errorf("failed to unmarshal user info: %v", err)
	}
	return &userInfo, nil
}
