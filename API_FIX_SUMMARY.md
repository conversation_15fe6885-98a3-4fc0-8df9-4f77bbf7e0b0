# WireGuard API修复总结

## 问题描述
调用 `/api/wg/start?device_name=beacon` 启动接口时，报错：
```json
{"error":"Enterprise ID cannot be empty"}
```

但原本的API设计中并未要求 `enterprise_id` 参数。

## 问题原因
在实现命名空间隔离时，错误地修改了 `StartWgInterface` 和 `StopWgInterface` 函数，要求提供 `enterprise_id` 参数，这破坏了原有的API兼容性。

## 修复方案

### 1. 添加了 `GetDeviceByName` 方法
在 `service/wireguardmgr/manager.go` 中添加：
```go
// GetDeviceByName 根据设备名称获取设备信息
func (m *Manager) GetDeviceByName(ctx context.Context, deviceName string) (*Device, error) {
    var device Device
    err := m.DevicesColl.FindOne(ctx, bson.M{"name": deviceName}).Decode(&device)
    if err != nil {
        return nil, fmt.Errorf("device not found: %v", err)
    }
    return &device, nil
}
```

### 2. 修复了控制器函数
更新 `controller/wgController.go` 中的 `StartWgInterface` 和 `StopWgInterface`：

**修复前**：
```go
func StartWgInterface(c *gin.Context) {
    deviceName := c.Query("device_name")
    enterpriseID := c.Query("enterprise_id")  // ❌ 要求enterprise_id
    if enterpriseID == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID cannot be empty"})
        return
    }
    // ...
}
```

**修复后**：
```go
func StartWgInterface(c *gin.Context) {
    deviceName := c.Query("device_name")
    if deviceName == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Device name cannot be empty"})
        return
    }

    // Get device by name to find namespace and enterprise info
    device, err := wgMgr.GetDeviceByName(context.TODO(), deviceName)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get device: %v", err)})
        return
    }

    err = wgMgr.Helper.StartInterface(deviceName, device.Namespace)
    // ...
}
```

## API使用方式

### 修复后的正确调用方式：
```bash
# 启动接口
curl -X POST "http://localhost:8080/api/wg/start?device_name=beacon"

# 停止接口  
curl -X POST "http://localhost:8080/api/wg/stop?device_name=beacon"
```

### 参数说明：
- `device_name`: 设备名称（必需）
- ~~`enterprise_id`: 企业ID（已移除要求）~~

## 工作流程

1. **接收请求**：API接收设备名称参数
2. **查找设备**：通过 `GetDeviceByName` 查找设备信息
3. **获取命名空间**：从设备信息中获取命名空间名称
4. **执行操作**：在正确的命名空间中启动/停止WireGuard接口

## 兼容性保证

- ✅ **向后兼容**：原有的API调用方式继续有效
- ✅ **自动查找**：自动从设备名称查找企业ID和命名空间
- ✅ **错误处理**：提供清晰的错误信息

## 测试验证

### 1. 基本功能测试
```bash
# 运行API修复测试脚本
./scripts/test-api-fix.sh
```

### 2. 手动测试
```bash
# 测试启动接口
curl -X POST "http://localhost:8080/api/wg/start?device_name=beacon"

# 预期响应
{"message":"Interface started successfully"}

# 测试停止接口
curl -X POST "http://localhost:8080/api/wg/stop?device_name=beacon"

# 预期响应
{"message":"Interface stopped successfully"}
```

### 3. 错误情况测试
```bash
# 测试空设备名
curl -X POST "http://localhost:8080/api/wg/start?device_name="
# 预期：{"error":"Device name cannot be empty"}

# 测试不存在的设备
curl -X POST "http://localhost:8080/api/wg/start?device_name=nonexistent"
# 预期：{"error":"Failed to get device: device not found: ..."}
```

## 相关文件修改

1. **`service/wireguardmgr/manager.go`**
   - 添加 `GetDeviceByName` 方法

2. **`controller/wgController.go`**
   - 修复 `StartWgInterface` 函数
   - 修复 `StopWgInterface` 函数

## 注意事项

1. **设备必须存在**：设备名称必须在数据库中存在
2. **命名空间支持**：设备必须有关联的命名空间信息
3. **权限要求**：API调用可能需要适当的认证和授权

## 后续建议

1. **监控日志**：观察API调用的详细日志
2. **性能测试**：验证数据库查询性能
3. **文档更新**：更新API文档以反映修复后的参数要求
