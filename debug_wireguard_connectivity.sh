#!/bin/bash

# WireGuard Namespace Connectivity Debug Script
# This script helps diagnose connectivity issues with namespace-isolated WireGuard instances

echo "=== WireGuard Namespace Connectivity Diagnostic ==="
echo "Date: $(date)"
echo

# Function to check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "❌ This script must be run as root for network diagnostics"
        echo "Please run: sudo $0"
        exit 1
    fi
    echo "✅ Running as root"
}

# Function to check basic network setup
check_basic_network() {
    echo "=== Basic Network Configuration ==="
    
    # Check IP forwarding
    if [ "$(cat /proc/sys/net/ipv4/ip_forward)" = "1" ]; then
        echo "✅ IP forwarding is enabled"
    else
        echo "❌ IP forwarding is disabled"
        echo "   Fix: echo 1 > /proc/sys/net/ipv4/ip_forward"
    fi
    
    # Check main network interface
    echo "📋 Network interfaces:"
    ip link show | grep -E "^[0-9]+:" | head -5
    
    echo "📋 Default route:"
    ip route show default
    echo
}

# Function to check WireGuard namespaces
check_wireguard_namespaces() {
    echo "=== WireGuard Namespaces ==="
    
    # List all namespaces
    echo "📋 All network namespaces:"
    ip netns list
    
    # List WireGuard namespaces
    echo "📋 WireGuard namespaces:"
    ip netns list | grep "^wg-" || echo "   No WireGuard namespaces found"
    echo
}

# Function to check veth interfaces
check_veth_interfaces() {
    echo "=== Veth Interfaces ==="
    
    echo "📋 Veth interfaces on host:"
    ip link show | grep -E "v[a-f0-9]{8}[hn]@" || echo "   No veth interfaces found"
    echo
}

# Function to check iptables rules
check_iptables_rules() {
    echo "=== IPTables Rules ==="
    
    echo "📋 NAT PREROUTING rules (DNAT):"
    iptables -t nat -L PREROUTING -n -v --line-numbers | head -10
    
    echo "📋 NAT POSTROUTING rules (SNAT/MASQUERADE):"
    iptables -t nat -L POSTROUTING -n -v --line-numbers | head -10
    
    echo "📋 FORWARD rules:"
    iptables -L FORWARD -n -v --line-numbers | head -10
    echo
}

# Function to check specific namespace
check_specific_namespace() {
    local ns_name="$1"
    if [ -z "$ns_name" ]; then
        echo "📋 Available WireGuard namespaces:"
        ip netns list | grep "^wg-"
        echo
        read -p "Enter namespace name to diagnose (or press Enter to skip): " ns_name
    fi
    
    if [ -z "$ns_name" ]; then
        return
    fi
    
    echo "=== Diagnosing Namespace: $ns_name ==="
    
    # Check if namespace exists
    if ! ip netns list | grep -q "^$ns_name$"; then
        echo "❌ Namespace $ns_name does not exist"
        return
    fi
    echo "✅ Namespace $ns_name exists"
    
    # Check interfaces in namespace
    echo "📋 Interfaces in namespace $ns_name:"
    ip netns exec "$ns_name" ip link show
    
    echo "📋 IP addresses in namespace $ns_name:"
    ip netns exec "$ns_name" ip addr show
    
    echo "📋 Routes in namespace $ns_name:"
    ip netns exec "$ns_name" ip route show
    
    # Check if WireGuard interface exists
    echo "📋 WireGuard interfaces in namespace $ns_name:"
    ip netns exec "$ns_name" wg show 2>/dev/null || echo "   No WireGuard interfaces found"
    
    echo
}

# Function to test connectivity
test_connectivity() {
    echo "=== Connectivity Tests ==="
    
    # Test external connectivity
    echo "📋 Testing external connectivity from host:"
    if ping -c 1 -W 2 ******* >/dev/null 2>&1; then
        echo "✅ Host can reach external networks"
    else
        echo "❌ Host cannot reach external networks"
    fi
    
    # Test DNS
    echo "📋 Testing DNS resolution:"
    if nslookup google.com >/dev/null 2>&1; then
        echo "✅ DNS resolution works"
    else
        echo "❌ DNS resolution failed"
    fi
    echo
}

# Function to show common fixes
show_common_fixes() {
    echo "=== Common Fixes ==="
    echo
    echo "🔧 If external clients cannot connect to WireGuard:"
    echo "   1. Check firewall rules:"
    echo "      ufw status"
    echo "      iptables -L -n"
    echo
    echo "   2. Ensure WireGuard port is open:"
    echo "      ufw allow <wireguard_port>/udp"
    echo "      # or for iptables:"
    echo "      iptables -A INPUT -p udp --dport <wireguard_port> -j ACCEPT"
    echo
    echo "   3. Check if the service is listening:"
    echo "      ss -ulnp | grep <wireguard_port>"
    echo
    echo "   4. Verify DNAT rules are correct:"
    echo "      iptables -t nat -L PREROUTING -n -v"
    echo
    echo "   5. Check namespace connectivity:"
    echo "      ip netns exec <namespace> ping <host_veth_ip>"
    echo
    echo "🔧 If WireGuard interface won't start:"
    echo "   1. Check configuration file:"
    echo "      wg-quick up <interface>"
    echo
    echo "   2. Check namespace isolation:"
    echo "      ip netns exec <namespace> wg show"
    echo
    echo "   3. Check for port conflicts:"
    echo "      ss -ulnp | grep <port>"
    echo
}

# Main execution
main() {
    check_root
    echo
    
    check_basic_network
    check_wireguard_namespaces
    check_veth_interfaces
    check_iptables_rules
    test_connectivity
    
    # Interactive namespace diagnosis
    check_specific_namespace "$1"
    
    show_common_fixes
    
    echo "=== Diagnostic Complete ==="
    echo "If you're still experiencing issues, please share this output with support."
}

# Run main function with first argument as namespace name
main "$1"
