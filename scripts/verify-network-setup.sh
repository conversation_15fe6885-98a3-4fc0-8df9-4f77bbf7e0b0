#!/bin/bash

# WireGuard Namespace Network Setup Verification Script
# This script verifies that the system is properly configured for namespace-based WireGuard

set -e

echo "=== WireGuard Namespace Network Setup Verification ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Check if running as root
echo "1. Checking root privileges..."
if [ "$EUID" -ne 0 ]; then
    print_status 1 "Root privileges required for namespace operations"
    echo "Please run with: sudo $0"
    exit 1
else
    print_status 0 "Running with root privileges"
fi

# Check if required tools are installed
echo
echo "2. Checking required tools..."

check_command() {
    if command -v $1 >/dev/null 2>&1; then
        print_status 0 "$1 is installed"
        return 0
    else
        print_status 1 "$1 is not installed"
        return 1
    fi
}

TOOLS_OK=true
check_command "ip" || TOOLS_OK=false
check_command "iptables" || TOOLS_OK=false
check_command "wg" || TOOLS_OK=false
check_command "wg-quick" || TOOLS_OK=false

if [ "$TOOLS_OK" = false ]; then
    echo
    echo "Install missing tools:"
    echo "  Ubuntu/Debian: sudo apt install iproute2 iptables wireguard-tools"
    echo "  CentOS/RHEL:   sudo yum install iproute2 iptables wireguard-tools"
    exit 1
fi

# Check IP forwarding
echo
echo "3. Checking IP forwarding..."
if [ "$(cat /proc/sys/net/ipv4/ip_forward)" = "1" ]; then
    print_status 0 "IP forwarding is enabled"
else
    print_status 1 "IP forwarding is disabled"
    echo "Enable with: echo 1 > /proc/sys/net/ipv4/ip_forward"
    echo "Make permanent by adding 'net.ipv4.ip_forward = 1' to /etc/sysctl.conf"
fi

# Check iptables functionality
echo
echo "4. Checking iptables functionality..."
if iptables -L >/dev/null 2>&1; then
    print_status 0 "iptables is functional"
else
    print_status 1 "iptables is not functional"
fi

# Check namespace support
echo
echo "5. Checking network namespace support..."
if ip netns list >/dev/null 2>&1; then
    print_status 0 "Network namespace support is available"
else
    print_status 1 "Network namespace support is not available"
fi

# Test namespace creation
echo
echo "6. Testing namespace operations..."
TEST_NS="wg-test-$$"

# Create test namespace
if ip netns add "$TEST_NS" 2>/dev/null; then
    print_status 0 "Can create network namespaces"
    
    # Test command execution in namespace
    if ip netns exec "$TEST_NS" ip link show lo >/dev/null 2>&1; then
        print_status 0 "Can execute commands in namespace"
    else
        print_status 1 "Cannot execute commands in namespace"
    fi
    
    # Cleanup test namespace
    ip netns delete "$TEST_NS" 2>/dev/null
else
    print_status 1 "Cannot create network namespaces"
fi

# Check available network interfaces
echo
echo "7. Available network interfaces:"
ip link show | grep -E "^[0-9]+:" | while read line; do
    iface=$(echo "$line" | cut -d: -f2 | tr -d ' ')
    state=$(echo "$line" | grep -o "state [A-Z]*" | cut -d' ' -f2)
    echo "   - $iface ($state)"
done

# Check for common interface names
echo
echo "8. Checking common interface configurations..."
COMMON_INTERFACES=("eth0" "ens3" "ens33" "enp0s3" "wlan0")
for iface in "${COMMON_INTERFACES[@]}"; do
    if ip link show "$iface" >/dev/null 2>&1; then
        state=$(ip link show "$iface" | grep -o "state [A-Z]*" | cut -d' ' -f2)
        if [ "$state" = "UP" ]; then
            print_status 0 "Interface $iface is UP"
        else
            print_warning "Interface $iface exists but is $state"
        fi
    fi
done

# Check WireGuard module
echo
echo "9. Checking WireGuard kernel module..."
if lsmod | grep -q wireguard; then
    print_status 0 "WireGuard kernel module is loaded"
elif modprobe wireguard 2>/dev/null; then
    print_status 0 "WireGuard kernel module loaded successfully"
else
    print_status 1 "WireGuard kernel module is not available"
    echo "Install with: sudo apt install wireguard (Ubuntu/Debian)"
fi

# Test veth pair creation
echo
echo "10. Testing veth pair creation..."
VETH_HOST="test-veth-host-$$"
VETH_NS="test-veth-ns-$$"

if ip link add "$VETH_HOST" type veth peer name "$VETH_NS" 2>/dev/null; then
    print_status 0 "Can create veth pairs"
    
    # Test moving veth to namespace
    if ip netns add "$TEST_NS" 2>/dev/null && \
       ip link set "$VETH_NS" netns "$TEST_NS" 2>/dev/null; then
        print_status 0 "Can move veth to namespace"
        ip netns delete "$TEST_NS" 2>/dev/null
    else
        print_status 1 "Cannot move veth to namespace"
    fi
    
    # Cleanup
    ip link delete "$VETH_HOST" 2>/dev/null
else
    print_status 1 "Cannot create veth pairs"
fi

# Check iptables NAT support
echo
echo "11. Checking iptables NAT support..."
if iptables -t nat -L >/dev/null 2>&1; then
    print_status 0 "iptables NAT table is available"
else
    print_status 1 "iptables NAT table is not available"
fi

# Summary
echo
echo "=== Verification Summary ==="
echo
echo "If all checks passed, your system is ready for WireGuard namespace implementation."
echo "If any checks failed, please address the issues before proceeding."
echo
echo "Next steps:"
echo "1. Configure your application's NetworkInterface setting"
echo "2. Ensure the application runs with root privileges"
echo "3. Test WireGuard device creation through the API"
echo
echo "For troubleshooting, check:"
echo "- System logs: journalctl -f"
echo "- Network interfaces: ip link show"
echo "- Namespaces: ip netns list"
echo "- Iptables rules: iptables -t nat -L -n -v"
