#!/bin/bash

# Basic namespace functionality test script
# This script tests the core namespace operations without complex networking

set -e

echo "=== Basic Namespace Functionality Test ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Please run as root: sudo $0"
    exit 1
fi

print_info "Testing basic namespace operations..."

# Test namespace creation
TEST_NS="wg-test-basic-$$"
print_info "Creating test namespace: $TEST_NS"

if ip netns add "$TEST_NS" 2>/dev/null; then
    print_status 0 "Namespace creation successful"
else
    print_status 1 "Namespace creation failed"
    exit 1
fi

# Test namespace listing
print_info "Checking if namespace appears in list..."
if ip netns list | grep -q "$TEST_NS"; then
    print_status 0 "Namespace listing successful"
else
    print_status 1 "Namespace not found in list"
fi

# Test command execution in namespace
print_info "Testing command execution in namespace..."
if ip netns exec "$TEST_NS" ip link show lo >/dev/null 2>&1; then
    print_status 0 "Command execution in namespace successful"
else
    print_status 1 "Command execution in namespace failed"
fi

# Test loopback setup
print_info "Setting up loopback interface..."
if ip netns exec "$TEST_NS" ip link set lo up 2>/dev/null; then
    print_status 0 "Loopback setup successful"
else
    print_status 1 "Loopback setup failed"
fi

# Test loopback connectivity
print_info "Testing loopback connectivity..."
if ip netns exec "$TEST_NS" ping -c 1 127.0.0.1 >/dev/null 2>&1; then
    print_status 0 "Loopback connectivity successful"
else
    print_status 1 "Loopback connectivity failed"
fi

# Test WireGuard tools in namespace
print_info "Testing WireGuard tools in namespace..."
if ip netns exec "$TEST_NS" wg --version >/dev/null 2>&1; then
    print_status 0 "WireGuard tools accessible in namespace"
else
    print_status 1 "WireGuard tools not accessible in namespace"
fi

# Test key generation
print_info "Testing WireGuard key generation..."
PRIVATE_KEY=$(wg genkey 2>/dev/null)
if [ $? -eq 0 ] && [ -n "$PRIVATE_KEY" ]; then
    print_status 0 "Private key generation successful"
    
    PUBLIC_KEY=$(echo "$PRIVATE_KEY" | wg pubkey 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$PUBLIC_KEY" ]; then
        print_status 0 "Public key generation successful"
    else
        print_status 1 "Public key generation failed"
    fi
else
    print_status 1 "Private key generation failed"
fi

# Test veth pair creation (basic)
print_info "Testing veth pair creation..."
VETH_HOST="test-veth-host-$$"
VETH_NS="test-veth-ns-$$"

if ip link add "$VETH_HOST" type veth peer name "$VETH_NS" 2>/dev/null; then
    print_status 0 "Veth pair creation successful"
    
    # Test moving veth to namespace
    if ip link set "$VETH_NS" netns "$TEST_NS" 2>/dev/null; then
        print_status 0 "Moving veth to namespace successful"
        
        # Test bringing up interfaces
        if ip link set "$VETH_HOST" up 2>/dev/null && \
           ip netns exec "$TEST_NS" ip link set "$VETH_NS" up 2>/dev/null; then
            print_status 0 "Bringing up veth interfaces successful"
        else
            print_status 1 "Bringing up veth interfaces failed"
        fi
    else
        print_status 1 "Moving veth to namespace failed"
    fi
    
    # Cleanup veth
    ip link delete "$VETH_HOST" 2>/dev/null || true
else
    print_status 1 "Veth pair creation failed"
fi

# Test iptables basic functionality
print_info "Testing iptables functionality..."
if iptables -L >/dev/null 2>&1; then
    print_status 0 "Iptables basic functionality working"
    
    # Test NAT table
    if iptables -t nat -L >/dev/null 2>&1; then
        print_status 0 "Iptables NAT table accessible"
    else
        print_status 1 "Iptables NAT table not accessible"
    fi
else
    print_status 1 "Iptables basic functionality failed"
fi

# Cleanup test namespace
print_info "Cleaning up test namespace..."
if ip netns delete "$TEST_NS" 2>/dev/null; then
    print_status 0 "Namespace cleanup successful"
else
    print_status 1 "Namespace cleanup failed"
fi

echo
echo "=== Test Summary ==="
echo "Basic namespace functionality test completed."
echo "If all tests passed, the core namespace operations should work."
echo
echo "To test with your application:"
echo "1. Set NetInterface to 'none' in config to skip networking setup"
echo "2. Create a device through the API"
echo "3. Check if namespace is created: sudo ip netns list"
echo "4. Test WireGuard in namespace: sudo ip netns exec {namespace} wg show"
echo
echo "If basic tests pass but application still hangs, check:"
echo "- Application logs for specific error messages"
echo "- Database connectivity"
echo "- File permissions for /etc/wireguard/"
echo "- Available disk space"
