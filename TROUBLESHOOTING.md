# WireGuard Namespace Implementation Troubleshooting Guide

## Problem: Device Creation Hangs After IP Allocation

### Symptoms
- Device creation process starts successfully
- IP allocation completes (e.g., "Allocated IP: ********/24")
- Process hangs and eventually times out
- No error messages in logs

### Diagnostic Steps

#### 1. Check System Prerequisites
```bash
# Run the verification script
sudo ./scripts/verify-network-setup.sh

# Test basic namespace functionality
sudo ./scripts/test-namespace-basic.sh
```

#### 2. Check Application Logs
Look for the last successful operation before hanging:
```bash
tail -f /var/log/your-application.log
```

Expected log sequence:
```
Starting to create device: test for enterprise: OZBdwJJKZ67MaCqd
Allocating subnet for enterprise: OZBdwJJKZ67MaCqd
Allocated IP: ********/24
Generating WireGuard keys for device: test
Successfully generated keys for device: test
Creating namespace for device: test with interface: ens33
Generated namespace name: wg-OZBdwJJKZ67MaCqd-test for device: test
Creating namespace: wg-OZBdwJJKZ67MaCqd-test
```

#### 3. Test with Simplified Configuration

**Step 1**: Use debug configuration to disable networking
```bash
# Copy debug config
cp config/debug-config.yaml config/config.yaml

# Restart application
sudo ./your-application
```

**Step 2**: Test device creation
```bash
curl -X POST http://localhost:8080/api/wireguard/device \
  -H "Content-Type: application/json" \
  -d '{
    "name": "debug-test",
    "enterprise_code": "debug-enterprise"
  }'
```

#### 4. Manual Namespace Testing
```bash
# Test namespace creation manually
sudo ip netns add test-manual-ns

# Test command execution
sudo ip netns exec test-manual-ns ip link show

# Test loopback setup
sudo ip netns exec test-manual-ns ip link set lo up
sudo ip netns exec test-manual-ns ping -c 1 127.0.0.1

# Cleanup
sudo ip netns delete test-manual-ns
```

#### 5. Check System Resources
```bash
# Check disk space
df -h

# Check memory usage
free -h

# Check process limits
ulimit -a

# Check for existing namespaces
sudo ip netns list
```

### Common Issues and Solutions

#### Issue 1: Permission Denied
**Symptoms**: "Operation not permitted" errors
**Solution**: 
```bash
# Ensure running as root
sudo ./your-application

# Check capabilities
sudo setcap cap_net_admin+ep ./your-application
```

#### Issue 2: WireGuard Tools Not Found
**Symptoms**: "wg: command not found" in namespace
**Solution**:
```bash
# Install WireGuard tools
sudo apt install wireguard-tools  # Ubuntu/Debian
sudo yum install wireguard-tools   # CentOS/RHEL
```

#### Issue 3: Iptables Rules Conflict
**Symptoms**: DNAT/SNAT setup fails
**Solution**:
```bash
# Check existing rules
sudo iptables -t nat -L -n -v

# Clear conflicting rules (CAUTION: This affects all iptables rules)
sudo iptables -t nat -F PREROUTING
sudo iptables -t nat -F POSTROUTING
sudo iptables -F FORWARD
```

#### Issue 4: Network Interface Not Found
**Symptoms**: "Device not found" when setting up networking
**Solution**:
```bash
# List available interfaces
ip link show

# Update config with correct interface name
# Edit config/config.yaml:
NetworkInterface: "eth0"  # Replace with actual interface
```

#### Issue 5: Namespace Already Exists
**Symptoms**: "File exists" error when creating namespace
**Solution**:
```bash
# List existing namespaces
sudo ip netns list

# Remove conflicting namespace
sudo ip netns delete wg-enterprise-device

# Clean up any remaining veth interfaces
sudo ip link show | grep veth | cut -d: -f2 | xargs -I {} sudo ip link delete {}
```

### Progressive Debugging Approach

#### Level 1: Basic Functionality
1. Use `NetworkInterface: "none"` in config
2. Test device creation without networking
3. Verify namespace creation: `sudo ip netns list`

#### Level 2: Add Networking
1. Set correct `NetworkInterface` in config
2. Test with single device
3. Check iptables rules: `sudo iptables -t nat -L -n -v`

#### Level 3: Full Integration
1. Test multiple devices
2. Test peer creation
3. Test external connectivity

### Emergency Recovery

#### Clean Up All Namespaces
```bash
# List all WireGuard namespaces
sudo ip netns list | grep "^wg-"

# Delete all WireGuard namespaces
sudo ip netns list | grep "^wg-" | cut -d' ' -f1 | xargs -I {} sudo ip netns delete {}

# Clean up veth interfaces
sudo ip link show | grep "veth-h-" | cut -d: -f2 | cut -d@ -f1 | xargs -I {} sudo ip link delete {}
```

#### Reset Iptables Rules
```bash
# CAUTION: This will remove ALL iptables rules
sudo iptables -t nat -F
sudo iptables -F
sudo iptables -X

# Restore basic rules (adjust as needed)
sudo iptables -P INPUT ACCEPT
sudo iptables -P FORWARD ACCEPT
sudo iptables -P OUTPUT ACCEPT
```

### Performance Optimization

#### Reduce Timeouts for Testing
Edit `service/wireguardmgr/namespace.go`:
```go
// Change timeout from 30s to 10s for faster debugging
output, err := nm.execCommandWithTimeout(10*time.Second, "ip", "netns", "add", nsName)
```

#### Enable Debug Logging
Add more verbose logging in critical functions:
```go
log.Printf("DEBUG: About to execute command: %v", cmdArgs)
```

### Getting Help

If the issue persists:

1. **Collect Debug Information**:
   ```bash
   # System info
   uname -a
   cat /etc/os-release
   
   # Network info
   ip addr show
   ip route show
   
   # WireGuard info
   wg --version
   lsmod | grep wireguard
   
   # Application logs
   tail -100 /var/log/your-application.log
   ```

2. **Test Minimal Example**:
   ```bash
   # Create namespace manually
   sudo ip netns add test-debug
   sudo ip netns exec test-debug ip link set lo up
   
   # Test WireGuard key generation
   wg genkey | wg pubkey
   ```

3. **Check for Known Issues**:
   - Kernel version compatibility
   - SELinux/AppArmor restrictions
   - Container environment limitations
   - Virtualization platform restrictions

### Contact Information

For additional support, provide:
- Operating system and version
- Kernel version (`uname -r`)
- WireGuard version (`wg --version`)
- Complete application logs
- Output of verification scripts
- Network configuration details
