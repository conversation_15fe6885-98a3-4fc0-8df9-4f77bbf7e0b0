# 使用轻量级的 Alpine 作为基础镜像
FROM alpine:latest

# 替换为国内镜像源
RUN echo "https://mirrors.aliyun.com/alpine/v3.21/main" > /etc/apk/repositories && \
    echo "https://mirrors.aliyun.com/alpine/v3.21/community" >> /etc/apk/repositories

# 安装必要的运行时依赖
RUN apk add --no-cache \
    tzdata \
    ca-certificates \
    libc6-compat \
    dumb-init \
    iptables \
    ip6tables \
    kmod \
    iptables-legacy \
    wireguard-tools

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# 切换到工作目录（提前创建 /app）
WORKDIR /app

# 创建必要的目录并设置权限
RUN chown -R appuser:appgroup /app

# 复制编译好的二进制文件
COPY main ./main
COPY config/config.yaml ./config/

# 设置权限
RUN chown -R appuser:appgroup /app \
    && chmod +x /app/main

# 添加 appuser 到 docker 组（如果需要访问 Docker socket）
RUN addgroup -S docker && adduser appuser docker

# 使用 iptables-legacy
RUN ln -sf /usr/sbin/iptables-legacy /usr/sbin/iptables && \
    ln -sf /usr/sbin/iptables-legacy-restore /usr/sbin/iptables-restore && \
    ln -sf /usr/sbin/iptables-legacy-save /usr/sbin/iptables-save && \
    ln -sf /usr/sbin/ip6tables-legacy /usr/sbin/ip6tables && \
    ln -sf /usr/sbin/ip6tables-legacy-restore /usr/sbin/ip6tables-restore && \
    ln -sf /usr/sbin/ip6tables-legacy-save /usr/sbin/ip6tables-save

# 设置健康检查
HEALTHCHECK --interval=1m --timeout=5s --retries=3 CMD /usr/bin/timeout 5s /bin/sh -c "/usr/bin/wg show | /bin/grep -q interface || exit 1"

# 切换到非 root 用户
USER appuser

# 暴露需要的端口（根据你的应用需求调整）
EXPOSE 1883 50000-59999

# 设置容器启动命令
CMD ["/usr/bin/dumb-init", "./main"]