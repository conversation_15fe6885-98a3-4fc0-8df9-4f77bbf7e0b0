package db

import (
	"beacon/cloud/models"
	"beacon/cloud/utils"
	"context"
	"database/sql"
	"log"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/crypto/bcrypt"
)

var (
	Taos    *sql.DB
	MongoDB *mongo.Database
	Redis   *redis.Client
)

func CreateInitialAdmin() {
	var admin models.User
	err := MongoDB.Collection("users").FindOne(context.TODO(), bson.M{"username": "beacon"}).Decode(&admin)
	if err == mongo.ErrNoDocuments {
		hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("beacon"), bcrypt.DefaultCost)
		enterpriseid, _ := utils.GenerateRandomString(16)
		admin = models.User{
			Username:     "beacon",
			Email:        "<EMAIL>",
			EnterpriseID: enterpriseid,
			Password:     string(hashedPassword),
			Role:         "superadmin",
		}
		_, err := MongoDB.Collection("users").InsertOne(context.TODO(), admin)
		if err != nil {
			log.Fatal("Could not create initial admin user")
		}
		log.Println("Initial admin user created")
	}
}

func Init() error {
	err := InitMongoDB()
	if err != nil {
		return err
	}
	CreateInitialAdmin()
	err = InitTaosDB()
	if err != nil {
		return err
	}
	err = InitRedis()
	if err != nil {
		return err
	}
	return nil
}
