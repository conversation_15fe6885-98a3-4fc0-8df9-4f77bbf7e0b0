package db

import (
	"beacon/cloud/config"
	"context"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	Ctx = context.Background()
)

func InitRedis() error {
	// 创建 Redis 客户端
	Redis = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", config.AppConfig.Redis.Host, config.AppConfig.Redis.Port),
		Password: config.AppConfig.Redis.Password,
		DB:       config.AppConfig.Redis.DB,
	})

	// 测试连接
	_, err := Redis.Ping(Ctx).Result()
	if err != nil {
		return err
	}
	log.Println("[Redis] Connected to Redis!")
	return nil
}

// 用户角色缓存相关方法

// SetUserRole 将用户角色信息存入Redis
func SetUserRole(username string, role string) error {
	// 用户角色的键格式：user:role:{username}
	key := fmt.Sprintf("user:role:%s", username)
	// 设置角色，并设置24小时过期时间
	return Redis.Set(Ctx, key, role, 24*time.Hour).Err()
}

// GetUserRole 从Redis获取用户角色，如果不存在则从数据库获取并缓存
func GetUserRole(username string) (string, error) {
	// 用户角色的键格式：user:role:{username}
	key := fmt.Sprintf("user:role:%s", username)
	role, err := Redis.Get(Ctx, key).Result()
	if err == redis.Nil {
		// 键不存在，从数据库获取
		// log.Printf("Role not found in Redis for user %s, fetching from database", username)

		// 从MongoDB获取用户角色
		role, err = getRoleFromDatabase(username)
		if err != nil {
			return "", fmt.Errorf("failed to get role from database: %v", err)
		}

		// 将角色缓存到Redis
		if err := SetUserRole(username, role); err != nil {
			log.Printf("Warning: Failed to cache role in Redis: %v", err)
			// 继续执行，不因缓存失败而影响流程
		}

		return role, nil
	} else if err != nil {
		// 其他错误
		return "", err
	}
	return role, nil
}

// getRoleFromDatabase 从数据库获取用户角色
func getRoleFromDatabase(username string) (string, error) {
	// 创建一个临时结构体来存储用户信息
	type User struct {
		Role string `bson:"role"`
	}

	var user User
	// 从MongoDB的users集合中查询用户角色
	filter := bson.M{"username": username}
	err := MongoDB.Collection("users").FindOne(Ctx, filter).Decode(&user)
	if err != nil {
		return "", fmt.Errorf("user not found or database error: %v", err)
	}

	if user.Role == "" {
		return "", fmt.Errorf("user found but role is empty")
	}

	return user.Role, nil
}

// RefreshUserRole 刷新用户角色的过期时间
func RefreshUserRole(username string) error {
	// 用户角色的键格式：user:role:{username}
	key := fmt.Sprintf("user:role:%s", username)
	// 刷新过期时间为24小时
	return Redis.Expire(Ctx, key, 24*time.Hour).Err()
}

// DeleteUserRole 删除用户角色缓存
func DeleteUserRole(username string) error {
	// 用户角色的键格式：user:role:{username}
	key := fmt.Sprintf("user:role:%s", username)
	return Redis.Del(Ctx, key).Err()
}
