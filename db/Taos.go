package db

import (
	"beacon/cloud/config"
	"database/sql"
	"fmt"
	"log"

	_ "github.com/taosdata/driver-go/v3/taosWS"
)

// ----------------------------------------------------Taos----------------------------------------------------

const messagesTable = `CREATE STABLE IF NOT EXISTS mqtt_messages (
		ts TIMESTAMP,
		client_id BINARY(50),
		topic_name BINARY(255),
		payload NCHAR(1024),
		qos TINYINT,
		is_alarm BOOL
	) TAGS (enterprise_id BINARY(24))`

const alarmTable = `CREATE STABLE IF NOT EXISTS alarm_log (
		ts TIMESTAMP,
		topic_name BINARY(128),
		current_value DOUBLE,
		description BINARY(255),
		alarm_type BINARY(10),
		alarm_contact BINARY(64),
		send_status BINARY(255)
	) TAGS (enterprise_id BINARY(24))`

const wgCollectorTable = `CREATE STABLE IF NOT EXISTS wgstats.device_stats (
		ts TIMESTAMP,
		rx_bytes BIGINT,
		tx_bytes BIGINT
	) TAGS (public_key BINARY(64),listen_port INT)`

const deviceLocationTable = `CREATE STABLE IF NOT EXISTS device_location (
		ts TIMESTAMP,
		latitude DOUBLE,
		longitude DOUBLE
	) TAGS (enterprise_id BINARY(24))`

var (
	STables = map[string]string{
		"messages":        "mqtt_messages",
		"alarm_log":       "alarm_log",
		"wgstats":         "wgstats.device_stats",
		"device_location": "device_location",
	}
)

func QuerySelectString(table, fields, options string) string {
	return fmt.Sprintf("SELECT %s FROM %s WHERE %s", fields, table, options)
}

func InsertQueryString(table, fields, values string) string {
	return fmt.Sprintf("INSERT INTO %s %s VALUES (%s)", table, fields, values)
}

func UpdateQueryString(table, fields, values string) string {
	return fmt.Sprintf("UPDATE %s SET %s WHERE %s", table, fields, values)
}

func DeleteQueryString(table, query string) string {
	return fmt.Sprintf("DELETE FROM %s WHERE %s", table, query)
}

// createTables creates all necessary tables in TDengine.
// Note: TDengine does not support PRIMARY KEY constraints, so they are removed.
func createMqttTables() error {
	// Create mqtt_messages table.
	if _, err := Taos.Exec(messagesTable); err != nil {
		return fmt.Errorf("failed to create messages table: %v", err)
	}
	if _, err := Taos.Exec(alarmTable); err != nil {
		return fmt.Errorf("failed to create alarm table: %v", err)
	}
	if _, err := Taos.Exec(wgCollectorTable); err != nil {
		return fmt.Errorf("failed to create wg collector table: %v", err)
	}
	if _, err := Taos.Exec(deviceLocationTable); err != nil {
		return fmt.Errorf("failed to create device location table: %v", err)
	}
	return nil
}

func reconnectTaosDB(dbName string) error {
	if Taos != nil {
		Taos.Close()
	}

	dsn := fmt.Sprintf("%s:%s@ws(%s:%s)/%s",
		config.AppConfig.TaosDB.Username,
		config.AppConfig.TaosDB.Password,
		config.AppConfig.TaosDB.Host,
		config.AppConfig.TaosDB.Port,
		dbName,
	)
	var err error
	Taos, err = sql.Open("taosWS", dsn)
	if err != nil {
		return fmt.Errorf("[Taos] failed to reconnect to TDengine: %v", err)
	}

	if err := Taos.Ping(); err != nil {
		return fmt.Errorf("[Taos] failed to ping TDengine after reconnection: %v", err)
	}

	return nil
}

// InitDB initializes the TDengine database connection, creates the database and tables.
func InitTaosDB() error {
	dsn := fmt.Sprintf("%s:%s@ws(%s:%s)/", config.AppConfig.TaosDB.Username, config.AppConfig.TaosDB.Password, config.AppConfig.TaosDB.Host, config.AppConfig.TaosDB.Port)
	var err error
	Taos, err = sql.Open("taosWS", dsn)
	if err != nil {
		log.Fatalln("[Taos] Failed to connect to " + dsn + "; ErrMessage: " + err.Error())
	}

	// Test the connection.
	if err := Taos.Ping(); err != nil {
		return fmt.Errorf("[Taos] failed to ping TDengine: %v", err)
	}
	log.Println("[Taos] Successfully connected to TDengine")

	// Create database if it does not exist.
	if _, err := Taos.Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s", config.AppConfig.TaosDB.DBName)); err != nil {
		return fmt.Errorf("[Taos] failed to create database: %v", err)
	}

	if _, err := Taos.Exec("CREATE DATABASE IF NOT EXISTS wgstats"); err != nil {
		return fmt.Errorf("[Taos] failed to create database: %v", err)
	}

	err = reconnectTaosDB(config.AppConfig.TaosDB.DBName)
	if err != nil {
		log.Fatalln("[Taos] Failed to connect to " + dsn + "; ErrMessage: " + err.Error())
	}

	Taos.Exec("ALTER DATABASE wgstats KEEP 10d;")

	// Initialize tables.
	if err := createMqttTables(); err != nil {
		return fmt.Errorf("[Taos] failed to create tables: %v", err)
	}

	log.Println("[Taos] Successfully created tables")

	return nil
}

// ----------------------------------------------------Close----------------------------------------------------
// CloseDB closes the TDengine database connection.
func CloseTaosDB() {
	if Taos != nil {
		Taos.Close()
	}
}
