# WireGuard Network Namespace Implementation

## Overview

This document describes the implementation of network namespace isolation for WireGuard server instances in the codebase. The implementation ensures that each WireGuard server interface runs independently in its own dedicated network namespace, providing complete isolation between different WireGuard instances.

## Key Changes Made

### 1. Network Namespace Management (`service/wireguardmgr/namespace.go`)

**New File Created**: A comprehensive namespace manager that handles:
- Creating and deleting network namespaces
- Setting up loopback interfaces within namespaces
- Creating veth pairs for namespace connectivity
- Executing commands within specific namespaces using `ip netns exec`
- Listing and managing namespace lifecycle

**Key Functions**:
- `CreateNamespace(nsName string)` - Creates a new network namespace
- `DeleteNamespace(nsName string)` - Removes a network namespace
- `ExecInNamespace(nsName, command string, args...)` - Executes commands in namespace
- `GenerateNamespaceName(deviceName, enterpriseID string)` - Creates unique namespace names

### 2. Enhanced Device Model (`service/wireguardmgr/model.go`)

**Changes Made**:
- Added `Namespace` field to the `Device` struct to store the associated network namespace name
- This enables tracking which namespace each WireGuard device belongs to

### 3. Namespace-Aware WireGuard Helper (`service/wireguardmgr/wghelper.go`)

**Major Enhancements**:
- **Removed wgctrl dependency**: Replaced all wgctrl library calls with custom command-line implementations
- **Added namespace support**: All WireGuard operations now accept a namespace parameter
- **Custom WireGuard information retrieval**: Implemented parsing of `wg show dump` output
- **Key generation functions**: Added `GenerateKeys()` and `GeneratePresharedKey()` methods

**Updated Functions**:
- `StartInterface(iface, namespace string)` - Starts WireGuard interface in specific namespace
- `StopInterface(iface, namespace string)` - Stops WireGuard interface in specific namespace
- `SyncConfig(iface, namespace string)` - Syncs configuration within namespace
- `IsInterfaceRunningInNamespace(iface, namespace string)` - Checks interface status in namespace

**New Functions**:
- `GetDeviceInfoInNamespace(iface, namespace string)` - Retrieves device info from namespace
- `CreateDeviceInNamespace(device *Device)` - Creates device with namespace setup
- `DeleteDeviceFromNamespace(device *Device)` - Cleans up device and namespace
- `parseWGDump(output string)` - Parses WireGuard dump output into structured data

### 4. Updated Manager Operations (`service/wireguardmgr/manager.go`)

**Key Changes**:
- **Device Creation**: Now creates a dedicated namespace for each device
- **Key Generation**: Uses custom key generation instead of wgctrl
- **Status Retrieval**: Replaced wgctrl-based status functions with namespace-aware implementations
- **Peer Management**: Updated all peer operations to work with namespaces
- **Device Cleanup**: Enhanced deletion to properly clean up namespaces

**Updated Functions**:
- `CreateDevice()` - Now includes namespace creation
- `DeleteDevice()` - Now includes namespace cleanup
- `GetInterfacesStatus()` - Uses namespace-aware device information retrieval
- `GetWGPeerStatus()` - Retrieves peer status from namespace-isolated interfaces
- `GetWgInterfaceBandwidth()` - Calculates bandwidth from namespace-specific data

### 5. Namespace-Aware Statistics Collection (`service/wireguardmgr/collector.go`)

**Complete Rewrite**:
- **Removed wgctrl dependency**: No longer uses wgctrl.Client
- **Database-driven collection**: Queries devices from database instead of system-wide discovery
- **Namespace-specific data**: Collects statistics from each device's dedicated namespace
- **Enhanced error handling**: Gracefully handles stopped or unavailable interfaces

**Updated Functions**:
- `CollectDeviceStats()` - Uses WGHelper instead of wgctrl client
- `collectAndInsertStats()` - Iterates through database devices and collects from namespaces
- `GetWgInterfaceBandwidth()` - Works with custom WGDeviceInfo struct

### 6. Controller Updates (`controller/wgController.go`)

**API Enhancements**:
- **Start/Stop Interface**: Updated to require enterprise_id parameter to locate device namespace
- **Enhanced validation**: Added enterprise_id validation for interface operations
- **Namespace-aware operations**: All interface operations now work within appropriate namespaces

## Technical Benefits

### 1. Complete Isolation
- Each WireGuard server instance runs in its own network namespace
- No interference between different enterprise instances
- Enhanced security through network-level isolation

### 2. Improved Scalability
- Multiple WireGuard instances can use the same port numbers in different namespaces
- Better resource management and conflict avoidance
- Cleaner separation of concerns

### 3. Enhanced Security
- Network traffic isolation between enterprises
- Reduced attack surface through namespace boundaries
- Better compliance with multi-tenant security requirements

### 4. Maintainability
- Removed dependency on wgctrl library that was limited to root namespace
- Custom implementations provide better control and flexibility
- Cleaner error handling and debugging capabilities

## Implementation Details

### Namespace Naming Convention
Namespaces are named using the pattern: `wg-{enterpriseID}-{deviceName}`

### Command Execution
All WireGuard commands are executed using `ip netns exec {namespace} {command}` pattern

### Configuration Management
- Configuration files remain in `/etc/wireguard/`
- Each namespace accesses the same config files but operates independently
- Synchronization happens within the appropriate namespace context

### Error Handling
- Graceful degradation when namespaces are not available
- Comprehensive logging for debugging namespace operations
- Rollback mechanisms for failed namespace creation

## Migration Considerations

### Backward Compatibility
- Existing devices without namespace field will need migration
- Empty namespace field falls back to root namespace operations
- Gradual migration path available

### Database Updates
- New `namespace` field added to Device model
- Existing devices can be updated to include namespace information
- No breaking changes to existing API contracts

## Usage Examples

### Creating a Device with Namespace
```go
device := &Device{
    Name: "wg-enterprise1",
    EnterpriseID: "enterprise1",
    // ... other fields
}
err := manager.CreateDevice(ctx, device)
// Device will be created in namespace: wg-enterprise1-wg-enterprise1
```

### Starting Interface in Namespace
```go
err := helper.StartInterface("wg-enterprise1", "wg-enterprise1-wg-enterprise1")
```

### Retrieving Status from Namespace
```go
deviceInfo, err := helper.GetDeviceInfoInNamespace("wg-enterprise1", "wg-enterprise1-wg-enterprise1")
```

## Network Configuration Details

### Namespace Connectivity Architecture

Each WireGuard namespace is connected to the host system through:

1. **Veth Pair**: Virtual Ethernet pair connecting namespace to host
   - Host side: `veth-h-{truncated-namespace}`
   - Namespace side: `veth-n-{truncated-namespace}`

2. **IP Address Assignment**:
   - Uses link-local range (169.254.x.x) for veth communication
   - Host side: `***********/30`
   - Namespace side: `***********/30`

3. **DNAT Rules**: Forward external traffic to namespace
   ```bash
   iptables -t nat -A PREROUTING -i {host_interface} -p udp --dport {listen_port} \
     -j DNAT --to-destination {namespace_ip}:{listen_port}
   ```

4. **SNAT Rules**: Enable return traffic from namespace
   ```bash
   iptables -t nat -A POSTROUTING -s {namespace_ip} -o {host_interface} -j MASQUERADE
   ```

5. **Forward Rules**: Allow traffic forwarding to namespace
   ```bash
   iptables -A FORWARD -d {namespace_ip} -p udp --dport {listen_port} -j ACCEPT
   ```

### Network Flow

1. **Incoming Connection**:
   ```
   External Client → Host Interface → DNAT → Veth Host → Veth NS → WireGuard in Namespace
   ```

2. **Outgoing Response**:
   ```
   WireGuard in Namespace → Veth NS → Veth Host → SNAT → Host Interface → External Client
   ```

### Prerequisites

Before deploying, ensure:

1. **Root Privileges**: Required for namespace and iptables operations
2. **IP Forwarding Enabled**:
   ```bash
   echo 1 > /proc/sys/net/ipv4/ip_forward
   # Make permanent in /etc/sysctl.conf:
   net.ipv4.ip_forward = 1
   ```

3. **Iptables Available**: For NAT and forwarding rules
4. **Host Interface Configuration**: Ensure `NetInterface` in config points to correct interface

### Configuration Requirements

Update your configuration file to include the correct network interface:

```yaml
# config.yaml
NetworkInterface: "eth0"  # Replace with your actual interface name
```

### Troubleshooting Network Issues

1. **Check Namespace Connectivity**:
   ```bash
   # Test veth connectivity
   sudo ip netns exec {namespace} ping ***********

   # Check routes in namespace
   sudo ip netns exec {namespace} ip route show
   ```

2. **Verify Iptables Rules**:
   ```bash
   # Check DNAT rules
   sudo iptables -t nat -L PREROUTING -n -v

   # Check SNAT rules
   sudo iptables -t nat -L POSTROUTING -n -v

   # Check forward rules
   sudo iptables -L FORWARD -n -v
   ```

3. **Test External Connectivity**:
   ```bash
   # Test UDP port accessibility
   nc -u {host_ip} {wireguard_port}
   ```

## Future Enhancements

1. **Namespace Resource Limits**: Implement resource constraints per namespace
2. **Advanced Routing**: Enhanced routing between namespaces when needed
3. **Monitoring Integration**: Namespace-specific monitoring and alerting
4. **Backup/Restore**: Namespace-aware backup and restore procedures
5. **Dynamic IP Allocation**: Implement dynamic IP range allocation for veth pairs
6. **IPv6 Support**: Add IPv6 support for namespace connectivity
